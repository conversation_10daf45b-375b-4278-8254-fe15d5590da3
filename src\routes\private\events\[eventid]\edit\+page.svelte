<!-- src/routes/events/edit/+page.svelte -->
<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Switch } from '$lib/components/ui/switch/index.js';
	import { Tabs, TabsContent, TabsList, TabsTrigger } from '$lib/components/ui/tabs';
	import Markdown from '$lib/components/Markdown.svelte';

	import { Upload, X } from 'lucide-svelte';
	import { enhance } from '$app/forms';

	export let data;

	let bannerImage = data.eventData.image_url ?? null;
	// let bannerFile: FileList;
	// let bannerFileInput: HTMLInputElement;
	// Data from DB set to variables to display in form
	let eventName = data.eventData.title;
	let shortDescription = data.eventData.short_description ?? '';
	let longDescription = data.eventData.long_description ?? '';
	let longDescriptionArabic = data.eventData.long_description_arabic ?? '';
	let startDate = data.eventData.start_date ?? '';
	let endDate = data.eventData.end_date ?? '';
	let startTime = data.eventData.start_time ?? '';
	let endTime = data.eventData.end_time ?? '';
	let location = data.eventData.location ?? '';
	let locationUrl = data.eventData.location_url ?? '';
	let imageUrl = data.eventData.image_url ?? '';
	let maxRegistrations = data.eventData.max_registrations ?? '';
	let isPublished: boolean = data.eventData.is_published;

	let formattedDateTime = '';

	// let previewUrl: string | null = null;
	let errorMessage: string | null = null;

	// function handleImagePreview(event: Event) {
	// 	const input = event.target as HTMLInputElement;
	// 	if (input.files && input.files[0]) {
	// 		const file = input.files[0];
	// 		const img = new Image();
	// 		img.src = URL.createObjectURL(file);

	// 		img.onload = () => {
	// 			if (file.size > 2 * 1024 * 1024) {
	// 				errorMessage = 'File size exceeds 2MB';
	// 				bannerImage = null;
	// 			} else if (img.width > 1920 || img.height > 1093) {
	// 				errorMessage = 'Image dimensions exceed 1920x1093 pixels';
	// 				bannerImage = null;
	// 			} else {
	// 				errorMessage = null;
	// 				bannerImage = img.src;
	// 			}
	// 		};
	// 	}
	// }

	// preview image
	// const preview = async (e) => {
	// 	const file = e.target.files[0];
	// 	const reader = new FileReader();

	// 	reader.onload = (e) => {
	// 		bannerImage = e.target.result;
	// 	};

	// 	reader.readAsDataURL(file);
	// };

	function removeImage() {
		bannerImage = null;
		imageUrl = '';
	}
</script>

<div class="min-h-screen p-4">
	<Card.Root class="mx-auto max-w-2xl">
		<Card.Header>
			<Card.Title>Edit Event</Card.Title>
		</Card.Header>
		<Card.Content>
			<form method="POST" class="space-y-6" enctype="multipart/form-data" use:enhance>
				<!-- Hidden Image URL -->
				<!-- <input type="hidden" name="imageUrl" bind:value={imageUrl} /> -->

				<!-- Banner Image Upload -->
				<div class="space-y-2">
					<Label>Event Banner</Label>
					<div class="space-y-4">
						{#if bannerImage}
							<div class="relative">
								<img
									src={bannerImage}
									alt="Banner preview"
									class="h-auto w-full rounded-md object-cover"
								/>
								<Button
									variant="destructive"
									size="icon"
									class="absolute right-2 top-2"
									onclick={removeImage}
								>
									<X class="h-4 w-4" />
								</Button>
							</div>
						{:else}
							<div class="rounded-md border-2 border-dashed border-muted p-8">
								<div class="flex flex-col items-center justify-center gap-2">
									<Upload class="h-8 w-8 text-muted-foreground" />
									<Label
										for="banner"
										class="cursor-pointer text-sm text-primary hover:text-primary/90"
										>Click to upload image</Label
									>
									<div class="flex flex-col items-center gap-2">
										<p class="text-xs text-muted-foreground">Recommended size: 1920x1093 pixels.</p>
										<p class="text-xs text-muted-foreground">
											Max file size: 2 MB Type: PNG, JPG up to 2MB
										</p>
									</div>

									<Input id="banner" name="banner" type="file" accept="image/*" />
								</div>
							</div>
						{/if}
					</div>
				</div>

				<!-- Event Name -->
				<div class="space-y-2">
					<Label for="eventName">
						Event Name / Title <span class="text-destructive">*</span>
					</Label>
					<Input
						id="eventName"
						name="eventName"
						required
						placeholder="Enter event name"
						bind:value={eventName}
					/>
				</div>

				<!-- Short Description -->
				<div class="space-y-2">
					<Label for="shortDescription">
						Short Description <span class="text-destructive">*</span>
					</Label>
					<Input
						id="shortDescription"
						name="shortDescription"
						bind:value={shortDescription}
						required
						placeholder="Enter a brief description (max 150 characters)"
					/>
				</div>

				<!-- Full Description -->
				<!-- <div class="space-y-2">
					<Label for="description">
						Full Description <span class="text-destructive">*</span>
					</Label>
					<Textarea
						id="longDescription"
						name="longDescription"
						required
						class="min-h-[120px]"
						placeholder="Enter detailed event description"
						bind:value={longDescription}
					></Textarea>
				</div> -->

				<!-- English Description MARKDOWN -->
				<div class="mb-4">
					<label for="longDescription" class="mb-1 block text-sm font-medium"
						>Event Description</label
					>
					<Tabs value="write" class="w-full">
						<TabsList>
							<TabsTrigger value="write">Write</TabsTrigger>
							<TabsTrigger value="preview">Preview</TabsTrigger>
						</TabsList>
						<TabsContent value="write">
							<Textarea
								id="longDescription"
								name="longDescription"
								bind:value={longDescription}
								placeholder="Write your event description using Markdown..."
								rows="10"
								class="w-full"
							/>
						</TabsContent>
						<TabsContent value="preview">
							<div class="min-h-[200px] rounded-md border p-4">
								<Markdown content={longDescription} />
							</div>
						</TabsContent>
					</Tabs>
				</div>

				<!-- Full Description Arabic -->
				<!-- <div class="space-y-2">
					<Label for="descriptionArbic">
						Full Description Arabic <span class="text-destructive">*</span>
					</Label>
					<Textarea
						id="longDescriptionArabic"
						name="longDescriptionArabic"
						class="min-h-[120px]"
						placeholder="Enter detailed event description"
						bind:value={longDescriptionArabic}
					></Textarea>
				</div> -->

				<!-- Arabic Description MARKDOWN -->
				<div class="mb-4">
					<label for="longDescriptionArabic" class="mb-1 block text-sm font-medium"
						>Event Description - Arabic</label
					>
					<Tabs value="write" class="w-full">
						<TabsList>
							<TabsTrigger value="write">Write</TabsTrigger>
							<TabsTrigger value="preview">Preview</TabsTrigger>
						</TabsList>
						<TabsContent value="write">
							<Textarea
								id="longDescriptionArabic"
								name="longDescriptionArabic"
								bind:value={longDescriptionArabic}
								placeholder="اكتب وصف الحدث باستخدام Markdown..."
								rows="10"
								class="w-full text-right"
								dir="rtl"
							/>
						</TabsContent>
						<TabsContent value="preview">
							<div class="min-h-[200px] rounded-md border p-4">
								<Markdown content={longDescriptionArabic} isRTL={true} />
							</div>
						</TabsContent>
					</Tabs>
					<p class="mt-1 text-right text-xs text-muted-foreground" dir="rtl">
						يدعم Markdown: **غامق**، *مائل*، [روابط](https://example.com)، إلخ.
					</p>
				</div>

				<!-- Date and Time -->
				<div class="space-y-4">
					<h3 class="font-medium">Date and Time</h3>

					<!-- Start Date and Time -->
					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div class="space-y-2">
							<Label for="startDate">Start Date</Label>
							<Input id="startDate" type="date" name="startDate" bind:value={startDate} required />
						</div>
						<div class="space-y-2">
							<Label for="startTime">Start Time</Label>
							<Input id="startTime" name="startTime" type="time" bind:value={startTime} required />
						</div>
					</div>

					<!-- End Date and Time -->
					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div class="space-y-2">
							<Label for="endDate">End Date</Label>
							<Input id="endDate" type="date" name="endDate" bind:value={endDate} required />
						</div>
						<div class="space-y-2">
							<Label for="endTime">End Time</Label>
							<Input id="endTime" name="endTime" type="time" bind:value={endTime} required />
						</div>
					</div>
				</div>

				<!-- Event End Date and Time -->
				<!-- <div class="space-y-2">
					<Label for="endDateTime">
						Event End Date and Time <span class="text-destructive">*</span>
					</Label>
					<div class="relative">
						<Calendar class="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
						<Input
							id="endDateTime"
							name="endDateTime"
							type="datetime-local"
							required
							class="pl-10"
							value="2024-09-26T10:45"
						/>
					</div>
				</div> -->

				<!-- Location -->
				<div class="space-y-2">
					<Label for="location">
						Location <span class="text-destructive">*</span>
					</Label>
					<Input
						id="location"
						name="location"
						required
						placeholder="Enter location or link if Online"
						bind:value={location}
					/>
				</div>

				<!-- Location URL-->
				<div class="space-y-2">
					<Label for="locationUrl">
						Location URL <span class="text-destructive">*</span>
					</Label>
					<Input
						id="locationUrl"
						name="locationUrl"
						required
						placeholder="Enter location or link if Online"
						bind:value={locationUrl}
					/>
				</div>

				<!-- Radio Group Private or Public -->
				<!-- <RadioGroup.Root class="gap-2" value="r1"> -->
				<!-- Radio card #1 -->
				<!-- <div
						class="relative flex w-full items-start gap-2 rounded-lg border border-input p-4 shadow-sm shadow-black/[.04] has-[[data-state=checked]]:border-ring"
					>
						<RadioGroup.Item
							value="r1"
							id="radio-08-r1"
							aria-describedby="radio-08-r1-description"
							class="order-1 after:absolute after:inset-0"
						/>
						<div class="grid grow gap-2">
							<Label for="radio-08-r1">Public</Label>
							<p id="radio-08-r1-description" class="text-xs text-muted-foreground">
								Event is visible on the website and can be searched by anyone.
							</p>
						</div>
					</div> -->
				<!-- Radio card #2 -->
				<!-- <div
						class="relative flex w-full items-start gap-2 rounded-lg border border-input p-4 shadow-sm shadow-black/[.04] has-[[data-state=checked]]:border-ring"
					>
						<RadioGroup.Item
							value="r2"
							id="radio-08-r2"
							aria-describedby="radio-08-r2-description"
							class="order-1 after:absolute after:inset-0"
						/>
						<div class="grid grow gap-2">
							<Label for="radio-08-r2">Private</Label>
							<p id="radio-08-r2-description" class="text-xs text-muted-foreground">
								Event is only available via the private link.
							</p>
						</div>
					</div> -->
				<!-- </RadioGroup.Root> -->

				<!-- Max Registrations -->
				<div class="space-y-2">
					<Label for="maxRegistrations">Maximum Registrations</Label>
					<Input
						id="maxRegistrations"
						type="number"
						name="maxRegistrations"
						bind:value={maxRegistrations}
						placeholder="Enter maximum number of registrations"
						min="0"
					/>
				</div>
				<!-- Published Status -->
				<div class="flex items-center justify-between">
					<Label for="isPublished">Published</Label>
					<Switch id="isPublished" name="isPublished" bind:checked={isPublished} />
				</div>

				<!-- Action Buttons -->
				<div class="flex flex-col space-y-3 pt-4">
					<Button type="submit" formaction="?/update">Save Changes</Button>
					<Button type="submit" formaction="?/delete" variant="destructive">Delete Event</Button>
				</div>
			</form>
		</Card.Content>
	</Card.Root>
</div>

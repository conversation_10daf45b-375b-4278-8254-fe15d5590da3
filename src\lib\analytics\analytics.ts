import { posthog, isPostHogAvailable } from './posthog';
import { browser } from '$app/environment';

// Analytics service for centralized event tracking
export class AnalyticsService {
	private static instance: AnalyticsService;

	private constructor() { }

	public static getInstance(): AnalyticsService {
		if (!AnalyticsService.instance) {
			AnalyticsService.instance = new AnalyticsService();
		}
		return AnalyticsService.instance;
	}

	// Helper method to safely track events
	private track(eventName: string, properties?: Record<string, any>): void {
		if (!browser || !isPostHogAvailable()) return;

		try {
			posthog.capture(eventName, {
				timestamp: new Date().toISOString(),
				...properties
			});
		} catch (error) {
			if (browser) {
				console.error('Analytics tracking error:', error);
			}
		}
	}

	// Page view tracking
	trackPageView(path: string, properties?: Record<string, any>): void {
		this.track('$pageview', {
			$current_url: path,
			...properties
		});
	}

	// Authentication events
	trackLoginAttempt(email?: string): void {
		this.track('login_attempt', { email });
	}

	trackLoginSuccess(userId?: string): void {
		this.track('login_success', { user_id: userId });
	}

	trackLoginFailure(error?: string): void {
		this.track('login_failure', { error });
	}

	trackLogout(): void {
		this.track('logout');
	}

	trackSignupAttempt(email?: string): void {
		this.track('signup_attempt', { email });
	}

	trackSignupSuccess(userId?: string): void {
		this.track('signup_success', { user_id: userId });
	}

	// Event page interactions
	trackEventPageView(eventId: string, eventSlug: string, eventTitle?: string): void {
		this.track('event_page_view', {
			event_id: eventId,
			event_slug: eventSlug,
			event_title: eventTitle
		});
	}

	trackEventDetailsViewed(eventId: string, section: string): void {
		this.track('event_details_viewed', {
			event_id: eventId,
			section
		});
	}

	// Registration form events
	trackFormStarted(eventId: string, formType: 'registration' | 'contact' = 'registration'): void {
		this.track('form_started', {
			event_id: eventId,
			form_type: formType
		});
	}

	trackFormFieldInteraction(eventId: string, fieldKey: string, fieldType: string): void {
		this.track('form_field_interaction', {
			event_id: eventId,
			field_key: fieldKey,
			field_type: fieldType
		});
	}

	trackFormValidationError(eventId: string, fieldKey: string, errorMessage: string): void {
		this.track('form_validation_error', {
			event_id: eventId,
			field_key: fieldKey,
			error_message: errorMessage
		});
	}

	trackFormSubmissionAttempt(eventId: string, formType: 'registration' | 'contact' = 'registration'): void {
		this.track('form_submission_attempt', {
			event_id: eventId,
			form_type: formType
		});
	}

	trackFormSubmissionSuccess(eventId: string, registrationId?: string): void {
		this.track('form_submission_success', {
			event_id: eventId,
			registration_id: registrationId
		});
	}

	trackFormSubmissionFailure(eventId: string, error: string): void {
		this.track('form_submission_failure', {
			event_id: eventId,
			error
		});
	}

	trackFormAbandoned(eventId: string, fieldsCompleted: number, totalFields: number): void {
		this.track('form_abandoned', {
			event_id: eventId,
			fields_completed: fieldsCompleted,
			total_fields: totalFields,
			completion_rate: fieldsCompleted / totalFields
		});
	}

	// Button and interaction tracking
	trackButtonClick(buttonName: string, location: string, properties?: Record<string, any>): void {
		this.track('button_click', {
			button_name: buttonName,
			location,
			...properties
		});
	}

	trackLinkClick(linkText: string, linkUrl: string, location: string): void {
		this.track('link_click', {
			link_text: linkText,
			link_url: linkUrl,
			location
		});
	}

	// Admin panel events
	trackAdminPageView(page: string, eventId?: string): void {
		this.track('admin_page_view', {
			admin_page: page,
			event_id: eventId
		});
	}

	trackEventCreated(eventId: string, eventTitle: string): void {
		this.track('admin_event_created', {
			event_id: eventId,
			event_title: eventTitle
		});
	}

	trackEventUpdated(eventId: string, changedFields: string[]): void {
		this.track('admin_event_updated', {
			event_id: eventId,
			changed_fields: changedFields
		});
	}

	trackEventPublished(eventId: string): void {
		this.track('admin_event_published', {
			event_id: eventId
		});
	}

	trackEventUnpublished(eventId: string): void {
		this.track('admin_event_unpublished', {
			event_id: eventId
		});
	}

	trackFormBuilderUsed(eventId: string, action: 'field_added' | 'field_removed' | 'field_updated' | 'form_saved'): void {
		this.track('admin_form_builder_used', {
			event_id: eventId,
			action
		});
	}

	// Guest management events
	trackGuestListViewed(eventId: string, guestCount: number): void {
		this.track('admin_guest_list_viewed', {
			event_id: eventId,
			guest_count: guestCount
		});
	}

	trackGuestDataExported(eventId: string, exportType: 'csv' | 'pdf'): void {
		this.track('admin_guest_data_exported', {
			event_id: eventId,
			export_type: exportType
		});
	}

	trackGuestDataImported(eventId: string, importedCount: number): void {
		this.track('admin_guest_data_imported', {
			event_id: eventId,
			imported_count: importedCount
		});
	}

	trackQRCodeScanned(eventId: string, scanType: 'check_in' | 'check_out'): void {
		this.track('admin_qr_code_scanned', {
			event_id: eventId,
			scan_type: scanType
		});
	}

	trackCertificateGenerated(eventId: string, certificateType: 'single' | 'bulk'): void {
		this.track('admin_certificate_generated', {
			event_id: eventId,
			certificate_type: certificateType
		});
	}

	// Error tracking
	trackError(errorType: string, errorMessage: string, context?: Record<string, any>): void {
		this.track('error_occurred', {
			error_type: errorType,
			error_message: errorMessage,
			...context
		});
	}

	trackAPIError(endpoint: string, statusCode: number, errorMessage: string): void {
		this.track('api_error', {
			endpoint,
			status_code: statusCode,
			error_message: errorMessage
		});
	}

	// Performance tracking
	trackPerformance(metric: string, value: number, context?: Record<string, any>): void {
		this.track('performance_metric', {
			metric,
			value,
			...context
		});
	}

	// User identification (for admin users)
	identifyUser(userId: string, properties?: Record<string, any>): void {
		if (!browser || !isPostHogAvailable()) return;

		try {
			posthog.identify(userId, properties);
		} catch (error) {
			console.error('User identification error:', error);
		}
	}

	// Reset user (on logout)
	resetUser(): void {
		if (!browser || !isPostHogAvailable()) return;

		try {
			posthog.reset();
		} catch (error) {
			console.error('User reset error:', error);
		}
	}
}

// Export singleton instance
export const analytics = AnalyticsService.getInstance();

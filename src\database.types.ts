export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      admins: {
        Row: {
          auth_id: string | null
          company_name: string | null
          created_at: string
          email: string
          first_name: string | null
          id: string
          last_name: string | null
          mobile: string | null
          name: string | null
          organization: string | null
        }
        Insert: {
          auth_id?: string | null
          company_name?: string | null
          created_at?: string
          email: string
          first_name?: string | null
          id?: string
          last_name?: string | null
          mobile?: string | null
          name?: string | null
          organization?: string | null
        }
        Update: {
          auth_id?: string | null
          company_name?: string | null
          created_at?: string
          email?: string
          first_name?: string | null
          id?: string
          last_name?: string | null
          mobile?: string | null
          name?: string | null
          organization?: string | null
        }
        Relationships: []
      }
      devices: {
        Row: {
          code: string | null
          created_at: string
          created_by: string | null
          event_id: string | null
          id: number
          name: string | null
          username: string | null
        }
        Insert: {
          code?: string | null
          created_at?: string
          created_by?: string | null
          event_id?: string | null
          id?: number
          name?: string | null
          username?: string | null
        }
        Update: {
          code?: string | null
          created_at?: string
          created_by?: string | null
          event_id?: string | null
          id?: number
          name?: string | null
          username?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "devices_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "admins"
            referencedColumns: ["auth_id"]
          },
          {
            foreignKeyName: "devices_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "devices_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "public_events"
            referencedColumns: ["id"]
          },
        ]
      }
      email_templates: {
        Row: {
          content: string | null
          created_at: string
          created_by: string | null
          event_id: string | null
          from_name: string | null
          id: number
          subject: string | null
          type: string | null
        }
        Insert: {
          content?: string | null
          created_at?: string
          created_by?: string | null
          event_id?: string | null
          from_name?: string | null
          id?: number
          subject?: string | null
          type?: string | null
        }
        Update: {
          content?: string | null
          created_at?: string
          created_by?: string | null
          event_id?: string | null
          from_name?: string | null
          id?: number
          subject?: string | null
          type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "email_templates_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "admins"
            referencedColumns: ["auth_id"]
          },
          {
            foreignKeyName: "email_templates_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "email_templates_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "public_events"
            referencedColumns: ["id"]
          },
        ]
      }
      events: {
        Row: {
          active_code: string | null
          auto_approve_waitlist: boolean | null
          created_at: string | null
          created_by: string | null
          end_date: string
          end_time: string
          event_uid: string | null
          id: string
          image_url: string | null
          is_private: boolean | null
          is_published: boolean | null
          location: string
          location_url: string | null
          long_description: string
          long_description_arabic: string | null
          max_registrations: number | null
          registration_open: boolean | null
          short_description: string
          start_date: string
          start_time: string
          status: string | null
          title: string
          title_slug: string
          updated_at: string | null
          waiting_list: boolean | null
        }
        Insert: {
          active_code?: string | null
          auto_approve_waitlist?: boolean | null
          created_at?: string | null
          created_by?: string | null
          end_date: string
          end_time: string
          event_uid?: string | null
          id?: string
          image_url?: string | null
          is_private?: boolean | null
          is_published?: boolean | null
          location: string
          location_url?: string | null
          long_description: string
          long_description_arabic?: string | null
          max_registrations?: number | null
          registration_open?: boolean | null
          short_description: string
          start_date: string
          start_time: string
          status?: string | null
          title: string
          title_slug: string
          updated_at?: string | null
          waiting_list?: boolean | null
        }
        Update: {
          active_code?: string | null
          auto_approve_waitlist?: boolean | null
          created_at?: string | null
          created_by?: string | null
          end_date?: string
          end_time?: string
          event_uid?: string | null
          id?: string
          image_url?: string | null
          is_private?: boolean | null
          is_published?: boolean | null
          location?: string
          location_url?: string | null
          long_description?: string
          long_description_arabic?: string | null
          max_registrations?: number | null
          registration_open?: boolean | null
          short_description?: string
          start_date?: string
          start_time?: string
          status?: string | null
          title?: string
          title_slug?: string
          updated_at?: string | null
          waiting_list?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "events_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "admins"
            referencedColumns: ["auth_id"]
          },
        ]
      }
      form_fields: {
        Row: {
          created_at: string | null
          display_order: number | null
          event_id: string
          field_key: string
          field_label: string
          field_options: Json | null
          field_type: string
          help_text: string | null
          id: string
          is_enabled: boolean | null
          is_required: boolean | null
          is_standard_field: boolean | null
          placeholder_text: string | null
          updated_at: string | null
          validation_rules: Json | null
        }
        Insert: {
          created_at?: string | null
          display_order?: number | null
          event_id: string
          field_key: string
          field_label: string
          field_options?: Json | null
          field_type: string
          help_text?: string | null
          id?: string
          is_enabled?: boolean | null
          is_required?: boolean | null
          is_standard_field?: boolean | null
          placeholder_text?: string | null
          updated_at?: string | null
          validation_rules?: Json | null
        }
        Update: {
          created_at?: string | null
          display_order?: number | null
          event_id?: string
          field_key?: string
          field_label?: string
          field_options?: Json | null
          field_type?: string
          help_text?: string | null
          id?: string
          is_enabled?: boolean | null
          is_required?: boolean | null
          is_standard_field?: boolean | null
          placeholder_text?: string | null
          updated_at?: string | null
          validation_rules?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "form_fields_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "form_fields_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "public_events"
            referencedColumns: ["id"]
          },
        ]
      }
      form_responses: {
        Row: {
          created_at: string | null
          event_id: string
          field_key: string
          field_value: string | null
          guest_id: number
          id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          event_id: string
          field_key: string
          field_value?: string | null
          guest_id: number
          id?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          event_id?: string
          field_key?: string
          field_value?: string | null
          guest_id?: number
          id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "form_responses_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "form_responses_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "public_events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "form_responses_guest_id_fkey"
            columns: ["guest_id"]
            isOneToOne: false
            referencedRelation: "guests"
            referencedColumns: ["id"]
          },
        ]
      }
      gates: {
        Row: {
          created_at: string
          id: number
          location_id: number | null
          name: string | null
        }
        Insert: {
          created_at?: string
          id?: number
          location_id?: number | null
          name?: string | null
        }
        Update: {
          created_at?: string
          id?: number
          location_id?: number | null
          name?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "gates_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
        ]
      }
      guests: {
        Row: {
          age: string | null
          back_id_photo_url: string | null
          business: string | null
          category: string | null
          check_in_date_time: string | null
          check_out_date_time: string | null
          created_at: string
          date_of_birth: string | null
          designation: string | null
          diet: string | null
          email: string | null
          event_id: string | null
          first_name: string | null
          front_id_photo_url: string | null
          gender: string | null
          id: number
          identity_type: string | null
          know: string | null
          last_name: string | null
          marketing: string | null
          middle_name: string | null
          mobile: string | null
          name: string | null
          name_on_badge: string | null
          national_id: string | null
          nationality: string | null
          nationality_form: string | null
          notes: string | null
          organization: string | null
          panels: string | null
          passport_number: string | null
          qr_data: string | null
          qr_data_label: string | null
          qr_url: string | null
          salutation: string | null
          stage: string | null
          status: string | null
          uid: number
        }
        Insert: {
          age?: string | null
          back_id_photo_url?: string | null
          business?: string | null
          category?: string | null
          check_in_date_time?: string | null
          check_out_date_time?: string | null
          created_at?: string
          date_of_birth?: string | null
          designation?: string | null
          diet?: string | null
          email?: string | null
          event_id?: string | null
          first_name?: string | null
          front_id_photo_url?: string | null
          gender?: string | null
          id?: number
          identity_type?: string | null
          know?: string | null
          last_name?: string | null
          marketing?: string | null
          middle_name?: string | null
          mobile?: string | null
          name?: string | null
          name_on_badge?: string | null
          national_id?: string | null
          nationality?: string | null
          nationality_form?: string | null
          notes?: string | null
          organization?: string | null
          panels?: string | null
          passport_number?: string | null
          qr_data?: string | null
          qr_data_label?: string | null
          qr_url?: string | null
          salutation?: string | null
          stage?: string | null
          status?: string | null
          uid?: number
        }
        Update: {
          age?: string | null
          back_id_photo_url?: string | null
          business?: string | null
          category?: string | null
          check_in_date_time?: string | null
          check_out_date_time?: string | null
          created_at?: string
          date_of_birth?: string | null
          designation?: string | null
          diet?: string | null
          email?: string | null
          event_id?: string | null
          first_name?: string | null
          front_id_photo_url?: string | null
          gender?: string | null
          id?: number
          identity_type?: string | null
          know?: string | null
          last_name?: string | null
          marketing?: string | null
          middle_name?: string | null
          mobile?: string | null
          name?: string | null
          name_on_badge?: string | null
          national_id?: string | null
          nationality?: string | null
          nationality_form?: string | null
          notes?: string | null
          organization?: string | null
          panels?: string | null
          passport_number?: string | null
          qr_data?: string | null
          qr_data_label?: string | null
          qr_url?: string | null
          salutation?: string | null
          stage?: string | null
          status?: string | null
          uid?: number
        }
        Relationships: [
          {
            foreignKeyName: "guests_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "guests_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "public_events"
            referencedColumns: ["id"]
          },
        ]
      }
      invalid_scans: {
        Row: {
          created_at: string | null
          gate: string | null
          id: number
          qr_data: string
          scan_type: string | null
          scanner_id: number | null
          scanner_name: string | null
          status: string | null
          timestamp: string
        }
        Insert: {
          created_at?: string | null
          gate?: string | null
          id?: number
          qr_data: string
          scan_type?: string | null
          scanner_id?: number | null
          scanner_name?: string | null
          status?: string | null
          timestamp: string
        }
        Update: {
          created_at?: string | null
          gate?: string | null
          id?: number
          qr_data?: string
          scan_type?: string | null
          scanner_id?: number | null
          scanner_name?: string | null
          status?: string | null
          timestamp?: string
        }
        Relationships: []
      }
      locations: {
        Row: {
          address: string | null
          created_at: string
          event_id: string | null
          id: number
          name: string | null
        }
        Insert: {
          address?: string | null
          created_at?: string
          event_id?: string | null
          id?: number
          name?: string | null
        }
        Update: {
          address?: string | null
          created_at?: string
          event_id?: string | null
          id?: number
          name?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "locations_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "locations_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "public_events"
            referencedColumns: ["id"]
          },
        ]
      }
      scanner_logs: {
        Row: {
          action_type: string | null
          check_point: string | null
          created_at: string
          device_id: string | null
          event_id: string | null
          guest_id: string | null
          id: number
          processed_by: string | null
        }
        Insert: {
          action_type?: string | null
          check_point?: string | null
          created_at?: string
          device_id?: string | null
          event_id?: string | null
          guest_id?: string | null
          id?: number
          processed_by?: string | null
        }
        Update: {
          action_type?: string | null
          check_point?: string | null
          created_at?: string
          device_id?: string | null
          event_id?: string | null
          guest_id?: string | null
          id?: number
          processed_by?: string | null
        }
        Relationships: []
      }
      scanners: {
        Row: {
          created_at: string | null
          gate: string | null
          id: number
          location: string | null
          mode: string
          name: string
          password_hash: string
          requires_validation: boolean
          updated_at: string | null
          username: string
        }
        Insert: {
          created_at?: string | null
          gate?: string | null
          id?: number
          location?: string | null
          mode?: string
          name: string
          password_hash: string
          requires_validation?: boolean
          updated_at?: string | null
          username: string
        }
        Update: {
          created_at?: string | null
          gate?: string | null
          id?: number
          location?: string | null
          mode?: string
          name?: string
          password_hash?: string
          requires_validation?: boolean
          updated_at?: string | null
          username?: string
        }
        Relationships: []
      }
      scans: {
        Row: {
          created_at: string | null
          gate: string | null
          id: number
          processed: boolean | null
          qr_data: string
          scan_signature: string | null
          scan_type: string
          scanner_id: number | null
          scanner_name: string | null
          status: string | null
          timestamp: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          gate?: string | null
          id?: number
          processed?: boolean | null
          qr_data: string
          scan_signature?: string | null
          scan_type: string
          scanner_id?: number | null
          scanner_name?: string | null
          status?: string | null
          timestamp: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          gate?: string | null
          id?: number
          processed?: boolean | null
          qr_data?: string
          scan_signature?: string | null
          scan_type?: string
          scanner_id?: number | null
          scanner_name?: string | null
          status?: string | null
          timestamp?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "scans_scanner_id_fkey"
            columns: ["scanner_id"]
            isOneToOne: false
            referencedRelation: "scanners"
            referencedColumns: ["id"]
          },
        ]
      }
      social_shares: {
        Row: {
          created_at: string
          event_id: string | null
          id: number
          share_image_url: string | null
          share_text: string | null
        }
        Insert: {
          created_at?: string
          event_id?: string | null
          id?: number
          share_image_url?: string | null
          share_text?: string | null
        }
        Update: {
          created_at?: string
          event_id?: string | null
          id?: number
          share_image_url?: string | null
          share_text?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "social_shares_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "social_shares_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "public_events"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          back_id_image: string | null
          back_passport_image: string | null
          created_at: string | null
          date_of_birth: string | null
          designation: string | null
          email: string
          front_id_image: string | null
          front_passport_image: string | null
          gender: string | null
          id: string
          identity_type: string | null
          mobile: string | null
          name: string
          national_id: string | null
          nationality: string | null
          organization: string | null
          passport_number: string | null
        }
        Insert: {
          back_id_image?: string | null
          back_passport_image?: string | null
          created_at?: string | null
          date_of_birth?: string | null
          designation?: string | null
          email: string
          front_id_image?: string | null
          front_passport_image?: string | null
          gender?: string | null
          id?: string
          identity_type?: string | null
          mobile?: string | null
          name: string
          national_id?: string | null
          nationality?: string | null
          organization?: string | null
          passport_number?: string | null
        }
        Update: {
          back_id_image?: string | null
          back_passport_image?: string | null
          created_at?: string | null
          date_of_birth?: string | null
          designation?: string | null
          email?: string
          front_id_image?: string | null
          front_passport_image?: string | null
          gender?: string | null
          id?: string
          identity_type?: string | null
          mobile?: string | null
          name?: string
          national_id?: string | null
          nationality?: string | null
          organization?: string | null
          passport_number?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      public_events: {
        Row: {
          created_at: string | null
          end_date: string | null
          end_time: string | null
          id: string | null
          image_url: string | null
          location: string | null
          location_url: string | null
          long_description: string | null
          max_registrations: number | null
          registration_open: boolean | null
          short_description: string | null
          start_date: string | null
          start_time: string | null
          title: string | null
          title_slug: string | null
          waiting_list: boolean | null
        }
        Insert: {
          created_at?: string | null
          end_date?: string | null
          end_time?: string | null
          id?: string | null
          image_url?: string | null
          location?: string | null
          location_url?: string | null
          long_description?: string | null
          max_registrations?: number | null
          registration_open?: boolean | null
          short_description?: string | null
          start_date?: string | null
          start_time?: string | null
          title?: string | null
          title_slug?: string | null
          waiting_list?: boolean | null
        }
        Update: {
          created_at?: string | null
          end_date?: string | null
          end_time?: string | null
          id?: string | null
          image_url?: string | null
          location?: string | null
          location_url?: string | null
          long_description?: string | null
          max_registrations?: number | null
          registration_open?: boolean | null
          short_description?: string | null
          start_date?: string | null
          start_time?: string | null
          title?: string | null
          title_slug?: string | null
          waiting_list?: boolean | null
        }
        Relationships: []
      }
    }
    Functions: {
      auto_close_registration: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      get_event_form_fields: {
        Args: { p_event_id: string }
        Returns: {
          id: string
          field_key: string
          field_label: string
          field_type: string
          field_options: Json
          is_required: boolean
          is_standard_field: boolean
          is_enabled: boolean
          display_order: number
          validation_rules: Json
          placeholder_text: string
          help_text: string
        }[]
      }
      get_guest_form_responses: {
        Args: { p_guest_id: number }
        Returns: {
          field_key: string
          field_label: string
          field_type: string
          field_value: string
        }[]
      }
      get_registration_status: {
        Args: { event_uuid: string }
        Returns: {
          is_open: boolean
          reason: string
          current_count: number
          max_count: number
          is_full: boolean
          waiting_list_enabled: boolean
        }[]
      }
      initialize_standard_fields: {
        Args: { p_event_id: string }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
  | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
  | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
    Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
  : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
    Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
  ? R
  : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
    DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
    DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R
    }
  ? R
  : never
  : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
  | keyof DefaultSchema["Tables"]
  | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
  : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
    Insert: infer I
  }
  ? I
  : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
    Insert: infer I
  }
  ? I
  : never
  : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
  | keyof DefaultSchema["Tables"]
  | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
  : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
    Update: infer U
  }
  ? U
  : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
    Update: infer U
  }
  ? U
  : never
  : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
  | keyof DefaultSchema["Enums"]
  | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
  : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
  | keyof DefaultSchema["CompositeTypes"]
  | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
  : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {},
  },
} as const

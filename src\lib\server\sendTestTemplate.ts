import { SendMailClient } from 'zeptomail';
import { ZEPTO_TOKEN } from '$env/static/private';


export async function TestEmailContent(firstName: string, lastName: string, email: string, content) {

    const url = 'api.zeptomail.com/';
    const token = ZEPTO_TOKEN;

    const client = new SendMailClient({ url, token });

    let name = `${firstName} ${lastName}`;

    try {
        const resp = await client.sendMail({

            from: {
                address: '<EMAIL>',
                name: 'Test',
            },
            to: [
                {
                    email_address: {
                        address: email,
                        name: name,
                    },
                },
            ],


            subject: "Sending with ZeptoMail to have good experience",

            htmlbody: content,
        });

        console.log('Em<PERSON> sent successfully:', resp);
        return { success: true, response: resp };
    } catch (error) {
        console.error('Error sending email:', error);
        return { success: false, error };
    }
}

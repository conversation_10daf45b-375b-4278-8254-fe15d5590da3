<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { Button } from '$lib/components/ui/button';

	export let data;
</script>

<div class="flex h-full flex-col">
	<header class="flex h-14 items-center gap-4 border-b bg-background px-6 lg:h-[60px]">
		<div class="flex flex-1 items-center gap-4">
			<h1 class="text-lg font-semibold">Event Dashboard - {data.eventTitle}</h1>
		</div>
	</header>
	<div class="flex-1 p-6">
		<div class="grid gap-4 md:grid-cols-5">
			<Button onclick={() => goto(`/private/events/${$page.params.eventid}`)} variant="outline"
				>Dashboard</Button
			>
			<Button onclick={() => goto(`/private/events/${$page.params.eventid}/edit`)} variant="outline"
				>Edit Event</Button
			>
			<Button
				onclick={() => goto(`/private/events/${$page.params.eventid}/form-builder`)}
				variant="outline">Form Builder</Button
			>
			<!-- <Button onclick={() => goto(`/${$page.params.eventid}`)} variant="outline">View Event</Button> -->
			<!-- #TODO:  FIX IT: need to sent the event id to the view event page.
			 The problem is the id is passed but the page is expecting the slug -->
			<!-- <Button onclick={() => goto(`/`)} variant="outline">View Event</Button> -->
			<Button
				onclick={() => goto(`/private/events/${$page.params.eventid}/guestlist`)}
				variant="outline">View Guest List</Button
			>
			<Button
				onclick={() => goto(`/private/events/${$page.params.eventid}/email`)}
				variant="outline">Email</Button
			>
			<Button
				onclick={() => goto(`/private/events/${$page.params.eventid}/social-share`)}
				variant="outline">Social Share</Button
			>

			<Button
				onclick={() => goto(`/private/events/${$page.params.eventid}/scanner`)}
				variant="outline">Scanner</Button
			>
			<Button
				onclick={() => goto(`/private/events/${$page.params.eventid}/settings`)}
				variant="outline">Settings</Button
			>
		</div>
		<!-- Slot moved inside the content area -->
		<div class="mt-6">
			<slot />
		</div>
	</div>
</div>

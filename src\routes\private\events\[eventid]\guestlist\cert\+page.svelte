<script>
	export let data;

	let certificateData = {
		recipientName: '',
		courseName: '',
		completionDate: '',
		certificateId: 'CERT-' + Math.random().toString(36).substr(2, 9).toUpperCase(),
		issuerName: 'Your'
	};

	let generating = false;
	let isBulkProcessing = false;
	let bulkResults = null;

	async function generateCertificate() {
		console.log('Client Side Certificate params: ', data);
		if (!certificateData.recipientName || !certificateData.courseName) {
			alert('Please fill in all required fields');
			return;
		}

		generating = true;
		try {
			//const response = await fetch('/api/certificate', {
			const response = await fetch(
				`/private/events/${data.params.eventid}/guestlist/api/certificate`,
				{
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify(certificateData)
				}
			);

			if (!response.ok) {
				throw new Error('Failed to generate certificate');
			}

			// Get the PDF blob
			const blob = await response.blob();

			// Create download link
			const url = URL.createObjectURL(blob);
			const link = document.createElement('a');
			link.href = url;
			link.download = `${certificateData.recipientName}-certificate.pdf`;
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
			URL.revokeObjectURL(url);
		} catch (error) {
			console.error('Error:', error);
			alert('Error generating certificate. Please try again.');
		}
		generating = false;
	}

	async function generateBulkCertificates() {
		if (!confirm('Are you sure you want to generate certificates for all attended guests?')) {
			return;
		}

		isBulkProcessing = true;
		try {
			const response = await fetch(
				`/private/events/${data.params.eventid}/guestlist/api/bulk-certificates`,
				{
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						workshopId: data.params.eventid // Replace with actual workshop ID
					})
				}
			);

			if (!response.ok) {
				throw new Error('Failed to generate bulk certificates');
			}

			bulkResults = await response.json();
			alert('Bulk certificate generation completed!');
		} catch (error) {
			console.error('Error:', error);
			alert('Error generating bulk certificates. Please try again.');
		} finally {
			isBulkProcessing = false;
		}
	}
</script>

<div class="container mx-auto p-8">
	<h1 class="mb-6 text-3xl font-bold">Digital Certificate Generator</h1>

	<div class="mx-auto max-w-2xl">
		<div class="mb-4">
			<label class="mb-2 block text-sm font-medium" for="recipientName">Attendee Name * </label>
			<input
				type="text"
				id="recipientName"
				bind:value={certificateData.recipientName}
				class="w-full rounded border p-2"
				placeholder="Enter recipient name"
				required
			/>
		</div>

		<div class="mb-4">
			<label class="mb-2 block text-sm font-medium" for="courseName"> Workshop Name * </label>
			<input
				type="text"
				id="courseName"
				bind:value={certificateData.courseName}
				class="w-full rounded border p-2"
				placeholder="Enter course name"
			/>
		</div>

		<div class="mb-4">
			<label class="mb-2 block text-sm font-medium" for="completionDate"> Completion Date </label>
			<input
				type="date"
				id="completionDate"
				bind:value={certificateData.completionDate}
				class="w-full rounded border p-2"
			/>
		</div>

		<!-- <div class="mb-4">
			<label class="mb-2 block text-sm font-medium" for="issuerName"> Issuer Name </label>
			<input
				type="text"
				id="issuerName"
				bind:value={certificateData.issuerName}
				class="w-full rounded border p-2"
				placeholder="Enter issuer name"
			/>
		</div> -->

		<button
			on:click={generateCertificate}
			disabled={generating}
			class="w-full rounded bg-blue-600 px-6 py-2 text-white hover:bg-blue-700 disabled:bg-blue-300"
		>
			{generating ? 'Generating Certificate...' : 'Generate PDF Certificate'}
		</button>
	</div>

	<div class="mt-8">
		<button
			on:click={generateBulkCertificates}
			disabled={isBulkProcessing}
			class="rounded bg-green-600 px-6 py-2 text-white hover:bg-green-700 disabled:bg-green-300"
		>
			{isBulkProcessing ? 'Processing...' : 'Generate Bulk Certificates'}
		</button>

		{#if bulkResults}
			<div class="mt-4">
				<h3 class="mb-2 text-lg font-semibold">Processing Results:</h3>
				<div class="space-y-2">
					{#each bulkResults.results as result}
						<div class="rounded p-2 {result.status === 'success' ? 'bg-green-100' : 'bg-red-100'}">
							Guest ID: {result.guestId} - {result.status}
							{#if result.certificateUrl}
								<a
									href={result.certificateUrl}
									class="ml-2 text-blue-600 hover:underline"
									target="_blank">View Certificate</a
								>
							{/if}
						</div>
					{/each}
				</div>
			</div>
		{/if}
	</div>
</div>

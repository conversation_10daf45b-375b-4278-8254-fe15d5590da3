import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON>and<PERSON> } from './$types';

export const POST: RequestHandler = async ({ request, locals: { supabase } }) => {
    try {
        const { scans } = await request.json();

        if (!scans || !Array.isArray(scans) || scans.length === 0) {
            return json({
                success: false,
                error: 'No scans provided or invalid format'
            }, { status: 400 });
        }

        // Process each scan in the array
        const results = [];
        const errors = [];

        for (const scan of scans) {
            try {
                // Insert the scan record into the database
                const { data, error } = await supabase
                    .from('scans')
                    .insert({
                        qr_data: scan.qr_data,
                        timestamp: scan.timestamp,
                        scanner_id: scan.scanner_id,
                        scanner_name: scan.scanner_name,
                        gate: scan.gate,
                        scan_type: scan.scan_type,
                        processed: true, // Mark as processed since we're syncing now
                        status: scan.status || 'synced' // Add status field
                    })
                    .select()
                    .single();

                if (error) {
                    console.error('Error syncing scan:', error);
                    errors.push({ scan, error: error.message });
                } else {
                    results.push(data);
                }
            } catch (scanError) {
                console.error('Error processing individual scan:', scanError);
                errors.push({ scan, error: 'Processing error' });
            }
        }

        return json({
            success: true,
            processed: results.length,
            failed: errors.length,
            results,
            errors: errors.length > 0 ? errors : undefined
        });
    } catch (error) {
        console.error('Error in sync-offline endpoint:', error);
        return json({ success: false, error: 'Internal server error' }, { status: 500 });
    }
};

<script lang="ts">
	import '../app.css';
	import { invalidate } from '$app/navigation';
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import Nav from '$lib/components/Nav.svelte';
	import { analytics } from '$lib/analytics';

	export let data;
	$: ({ session, supabase } = data);

	// Track page views when route changes
	$: if ($page.url.pathname && typeof document !== 'undefined') {
		analytics.trackPageView($page.url.pathname, {
			page_title: document.title,
			referrer: document.referrer
		});
	}

	onMount(() => {
		console.log('v0.0.28 -- PostHog Integration Fixed');

		// Initialize analytics (PostHog)
		analytics.init();

		// Track initial page load performance using modern Performance API
		if (
			typeof window !== 'undefined' &&
			window.performance &&
			window.performance.getEntriesByType
		) {
			const navigationEntries = window.performance.getEntriesByType(
				'navigation'
			) as PerformanceNavigationTiming[];
			if (navigationEntries.length > 0) {
				const loadTime = navigationEntries[0].loadEventEnd - navigationEntries[0].fetchStart;
				if (loadTime > 0) {
					analytics.trackPageLoadTime(loadTime);
				}
			}
		}

		const { data } = supabase.auth.onAuthStateChange((_, newSession) => {
			if (newSession?.expires_at !== session?.expires_at) {
				invalidate('supabase:auth');
			}
		});

		return () => data.subscription.unsubscribe();
	});
</script>

<header
	class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
>
	<Nav />
</header>
<slot />
<!-- <Footer /> -->

<script lang="ts">
	import { onMount } from 'svelte';
	import <PERSON> from 'papaparse';
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import * as Card from '$lib/components/ui/card/index.js';
	import * as Table from '$lib/components/ui/table/index.js';

	let fileInput: HTMLInputElement;
	let fileName = '';
	let csvData: Record<string, string>[] = [];
	let rowCount = 0;

	function readFile() {
		const file = fileInput?.files?.[0];
		if (file) {
			fileName = file.name; // Set file name immediately
			Papa.parse(file, {
				complete: (results) => {
					csvData = results.data.filter((row) => Object.keys(row).length > 0); // Remove empty rows
					rowCount = csvData.length;
				},
				header: true,
				transformHeader: (header) => header.trim().toLowerCase().replace(/\s+/g, '')
			});
		}
	}

	async function handleSendEmail() {
		// the fetch goes to Form Action in the +page.server.ts
		const response = await fetch('?/sendEmails', {
			method: 'POST',
			body: JSON.stringify(csvData)
		});

		const result = await response.json();
		console.log('Result from server:', result);

		if (result.status) {
			alert(`Emails sent successfully! ${result.data.message}`);
		} else {
			alert(`Error sending emails: ${result.data.message}`);
		}
	}

	onMount(() => {
		fileInput = document.getElementById('csvFile') as HTMLInputElement;
	});
</script>

<Card.Root class="mx-auto w-full ">
	<Card.Header>
		<Card.Title>CSV File Processor and Email Sender</Card.Title>
	</Card.Header>
	<Card.Content class="space-y-6">
		<div class="flex items-center gap-4">
			<div class="flex-1">
				<input type="file" id="csvFile" accept=".csv" class="sr-only" onchange={readFile} />
				<label
					for="csvFile"
					class="inline-flex h-9 cursor-pointer items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-medium shadow-sm hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
				>
					Choose File {fileName ? `: ${fileName}` : ''}
				</label>
			</div>
		</div>

		<div class="flex gap-2">
			<Button variant="secondary" onclick={() => {}}>Add to Guest List</Button>
			<Button variant="secondary" onclick={() => {}}>Only Send Email</Button>
			<Button onclick={handleSendEmail}>Add to Guest List & Send Email</Button>
		</div>

		{#if csvData.length > 0}
			<div class="space-y-2">
				<div class="text-sm text-muted-foreground">
					CSV Content
					<br />
					Total rows: {rowCount}
				</div>
				<div class="rounded-lg border">
					<Table.Root>
						<Table.Header>
							<Table.Row>
								{#each Object.keys(csvData[0]) as header}
									<Table.Head>{header}</Table.Head>
								{/each}
							</Table.Row>
						</Table.Header>
						<Table.Body>
							{#each csvData as row}
								<Table.Row>
									{#each Object.keys(csvData[0]) as key}
										<Table.Cell>{row[key]}</Table.Cell>
									{/each}
								</Table.Row>
							{/each}
						</Table.Body>
					</Table.Root>
				</div>
			</div>
		{/if}
	</Card.Content>
</Card.Root>

// TODO: Need to Fix the First Name, Last Name and the Email address that its been sent to.
import { generateCertificatePDF } from './certificate';
import { supabaseAdmin } from '$lib/server/db/supabaseAdmin';
import { ZEPTO_TOKEN } from '$env/static/private';

const ZEPTOMAIL_URL = 'https://api.zeptomail.com/v1.1/email';
const ZEPTOMAIL_TOKEN = ZEPTO_TOKEN;

export async function generateAndSendBulkCertificates(eventId) {
	try {
		console.log('Event ID from Bulk Certificate: ', eventId);
		// <PERSON>tch attended guests from Supabase
		const { data: guests, error } = await supabaseAdmin
			.from('guests')
			.select(`id, first_name,last_name, email, events(title)`)
			.eq('event_id', eventId)
			.eq('status', 'Checked-In');
		// .eq('email', '<EMAIL>');

		console.log('Bulk Data Read: ', guests);

		if (error) throw error;

		// Process each guest
		const results = await Promise.all(
			guests.map(async (guest) => {
				try {
					console.log('Start Generate Certificate Data');
					// Generate certificate data
					const certificateData = {
						recipientName: guest.first_name + ' ' + guest.last_name,
						courseName: guest.events.title,
						completionDate: new Date().toISOString(),
						certificateId: `CERT-${eventId}-${guest.id}`
					};
					console.log('Start Generate Certificate PDF');
					// Generate PDF
					const pdfBuffer = await generateCertificatePDF(certificateData);

					// console.log('PDF Buffer: ', pdfBuffer);

					console.log('Start Certificate Upload');
					// Upload to Supabase Storage
					const filePath = `${eventId}/${certificateData.certificateId}.pdf`;
					const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
						.from('certificates')
						.upload(filePath, pdfBuffer, {
							contentType: 'application/pdf',
							upsert: true
						});

					if (uploadError) throw uploadError;

					console.log('Start  Certificate get Public URL');
					// Get public URL
					const {
						data: { publicUrl }
					} = supabaseAdmin.storage.from('certificates').getPublicUrl(filePath);

					console.log('Public Link for PDF: ', publicUrl);

					console.log('Start  Certificate Email Sending');
					// Send email via Zeptomail
					await sendCertificateEmail(guest.email, {
						name: guest.first_name + ' ' + guest.last_name,
						certificateUrl: publicUrl,
						workshopName: guest.events.title
					});

					return {
						guestId: guest.id,
						status: 'success',
						certificateUrl: publicUrl
					};
				} catch (error) {
					console.error(`Error processing guest ${guest.id}:`, error);
					return {
						guestId: guest.id,
						status: 'error',
						error: error.message
					};
				}
			})
		);

		return results;
	} catch (error) {
		console.error('Bulk certificate generation error:', error);
		throw error;
	}
}

async function sendCertificateEmail(email, { name, certificateUrl, workshopName }) {
	console.log('Data Received for Email.');
	console.log('Email:', email);
	console.log('Name:', name);
	console.log('URL:', certificateUrl);
	console.log('Workshop Name:', workshopName);

	const emailData = {
		from: {
			address: '<EMAIL>',
			name: 'Your Workshop Certificate'
		},
		to: [
			{
				email_address: {
					address: email,
					name: name
				}
			}
		],
		subject: `Your Certificate for ${workshopName}`,
		htmlbody: `
      <div style="font-family: Arial, sans-serif; padding: 20px;">
        <h2>Your Certificate is Ready!</h2>
        <p>Dear ${name},</p>
        <p>Thank you for attending ${workshopName}. Your certificate is now available.</p>
        <p>You can download your certificate using the link below:</p>
        <p><a href="${certificateUrl}" style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">Download Certificate</a></p>
        <p>This link will remain active for 30 days.</p>
        <p>Best regards,<br>Workshop Team</p>
      </div>
    `
	};

	console.log('Email Body: ', emailData);

	const response = await fetch(ZEPTOMAIL_URL, {
		method: 'POST',
		headers: {
			Accept: 'application/json',
			'Content-Type': 'application/json',
			Authorization: `${ZEPTOMAIL_TOKEN}`
		},
		body: JSON.stringify(emailData)
	});

	console.log('Response: ', response);

	if (!response.ok) {
		const error = await response.json();
		// throw new Error(`Email sending failed: ${error.message}`);
		throw new Error(`Email sending failed: ${error}`);
	}

	return response.json();
}

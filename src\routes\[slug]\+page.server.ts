import type { PageServerLoad } from './$types';
import { error, fail, redirect, type Actions } from '@sveltejs/kit';
import { z } from 'zod';
import { superValidate, message } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { registerUser } from '$lib/server/register';
import { getEnabledFormFields } from '$lib/server/formFields';
import { generateDynamicSchema } from '$lib/server/dynamicValidation';
import { getRegistrationStatus } from '$lib/server/registrationStatus';



export const load = (async ({ params, locals: { supabase }, cookies }) => {
    const { slug } = params;

    // First, try to get the event (published events only)
    const { data: eventArray, error: eventsError } = await supabase
        .from('events')
        .select('id, title, long_description,long_description_arabic, short_description, image_url, start_date, end_date, start_time, end_time, location, location_url, max_registrations, waiting_list, is_published, is_private, registration_open, active_code')
        .eq('title_slug', slug)
        .eq('is_published', true); // Only published events

    if (eventsError) {
        console.error('Error Querying Events Data: ', eventsError);
        throw error(500, 'Event not found')
        // return { error };
    }
    const event = eventArray[0];

    if (!event) {
        throw error(404, 'Event not found');
    }

    // Handle private events with secure server-side verification
    if (event.is_private) {
        // Check if user has verified access for this specific event
        const verificationCookie = cookies.get(`private_event_${event.id}`);
        const isVerified = verificationCookie === event.active_code;

        if (!isVerified) {
            redirect(303, `/${slug}/private`);
        }
        // If verified, continue to show the event
    }

    // Load form fields for this event
    const formFieldsResult = await getEnabledFormFields(event.id);
    const formFields = formFieldsResult.data || [];

    // Generate dynamic schema based on form fields
    let dynamicSchema = userFormSchema; // fallback to old schema
    let form;

    if (formFields.length > 0) {
        try {
            dynamicSchema = generateDynamicSchema(formFields);
            form = await superValidate(zod(dynamicSchema));
        } catch (err) {
            console.error('Error generating dynamic schema:', err);
            // Fallback to old schema
            form = await superValidate(zod(userFormSchema));
        }
    } else {
        form = await superValidate(zod(userFormSchema));
    }

    // console.log('Event Array: ', eventArray)

    // console.log('Event Data: ', event)

    console.log('event ID: ', event?.id)

    // Check current registrations

    const { data: count, error: regError } = await supabase
        .from('guests')
        .select('count', { count: 'exact' })
        .eq('event_id', event.id)
        .in('status', ['registered', 'imported']);

    // console.log('Count: ', count)
    // console.log('Max Registrations: ', event.max_registrations)
    // console.log('Waiting List: ', event.waiting_list)

    if (regError) throw error(500, 'Error fetching registrations');

    // Set waiting list condition - only show if there's actually a capacity limit
    let waitingList = false;
    if (event.max_registrations !== null && event.max_registrations > 0) {
        waitingList = event.waiting_list && count[0].count >= event.max_registrations;
    }

    console.log('Waiting List from condition: ', waitingList)
    console.log('Max registrations: ', event.max_registrations)
    console.log('Current count: ', count[0].count)

    // Get registration status for the event
    const registrationStatus = await getRegistrationStatus(supabase, event.id);

    return {
        event,
        form,
        waitingList,
        formFields,
        registrationStatus
    };
}) satisfies PageServerLoad;




// Form User Schema
const userFormSchema = z.object({
    // name: z.string().regex(/^[A-Za-z\s]+$/, "Special characters not allowed").min(3, 'Full name is required'),
    firstName: z.string().regex(/^[A-Za-z][A-Za-z' -]*$/, "Special characters not allowed").min(1, 'First name is required'),
    lastName: z.string().regex(/^[A-Za-z\s]+$/, "Special characters not allowed").min(3, 'Last name is required'),
    email: z.string().email('Invalid email address'),
    mobile: z
        .string()
        .regex(/^\+?[0-9]{8,14}$/, 'Invalid mobile number'),
    organization: z.string().regex(/^[A-Za-z\s]+$/, "Special characters not allowed").min(3, 'Organization is required'),
    eventId: z.string(),
    // gender: z.string(),
    // nationality: z.string(),
    // business: z.string(),
    // category: z.string(),
    // stage: z.string(),
    // know: z.string()
});

// Form Actions
export const actions: Actions = {
    default: async function ({ request }) {
        try {
            console.log('Form Action Started')
            const form = await superValidate(request, zod(userFormSchema));
            // console.log('Form data received before validate: ', form);

            if (!form.valid) {
                // Again, return { form } and things will just work.
                console.error('Form Validation Error')
                return fail(400, { form });
            }
            console.log('Form Validation Completed')
            // console.log('Form data after validate: ', form.data);

            // Setting qatar time to db entry
            // const formattedQatarTime = await getFormattedQatarTime()


            // console.log('Create a new Row with User Data')


            const result = await registerUser(form)
            // console.log('Result from registerUser: ', result)

            return message(form, 'success');


        }
        catch (error) {
            console.error('Error from actions:', error);
            return { success: false };
        }
    }
};
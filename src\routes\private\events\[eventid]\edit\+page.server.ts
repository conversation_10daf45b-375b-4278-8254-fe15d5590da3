import { error, redirect, type Actions } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load = (async ({ locals: { supabase, safeGetSession }, params }) => {
	console.log('The Edit Page is loading...');

	const { session } = await safeGetSession();
	if (!session) {
		redirect(303, '/login');
	}

	// console.log('Passed Params id (FROM EDIT PAGE): ', params.eventid);

	const { data: eventData, error: eventDataError } = await supabase
		.from('events')
		.select(`*`)
		.eq('id', params.eventid)
		.single();

	if (eventDataError) {
		console.error(eventDataError);
	}

	return { session, eventData };
}) satisfies PageServerLoad;




export const actions: Actions = {
	update: async ({ request, locals: { supabase, safeGetSession, }, params }) => {
		// console.log('Update Action called..')
		// console.log('Params from Update Action: ', params);
		const { session } = await safeGetSession();
		if (!session) {
			redirect(303, '/');
		}


		const formData = await request.formData();
		// console.log('-------------------------------------------------')
		// console.log('Form Data: ', formData);
		// console.log('-------------------------------------------------')
		const eventName = formData.get('eventName') as string;
		const shortDescription = formData.get('shortDescription') as string;
		const longDescription = formData.get('longDescription') as string;
		const longDescriptionArabic = formData.get('longDescriptionArabic') as string;
		const StartDate = formData.get('startDate') as string;
		const endDate = formData.get('endDate') as string;
		const startTime = formData.get('startTime') as string;
		const endTime = formData.get('endTime') as string;
		const location = formData.get('location') as string;
		const locationUrl = formData.get('locationUrl') as string;
		let maxRegistrations = formData.get('maxRegistrations') as string;
		let isPublished = formData.get('isPublished');
		// const imageFile = formData.get('imageUpload') as File | null;
		const bannerFile = formData.get('banner') as File;

		// console.log('+-+-+-+-+-+-+-+-')
		// console.log('bannerFile: ', bannerFile);
		// console.log('+-+-+-+-+-+-+-+-')

		let isPublishedValue: boolean;

		if (isPublished === 'on') {
			isPublishedValue = true;
		} else {
			isPublishedValue = false;

		}

		let imageUrl: string;

		// console.log('isPublished Value: ', isPublishedValue);
		// Check for new image upload

		// let imageUrl = formData.get('imageUrl') as string; // Existing image URL
		// console.log('Image URL: ', imageUrl);
		// console.log('Image File: ', imageFile);

		// if (maxRegistrations === '') {
		// 	const maxRegistrationsInt = Number(0);
		// }


		// if (!imageFile || imageFile.length === 0) {
		if (!bannerFile) {
			console.log('Image File is empty');
		}


		if (bannerFile) {
			// Upload the new image to Supabase storage
			// const filePath = `events/${params.eventid}/${imageFile.name}`;
			const fileExt = bannerFile.name.split('.').pop();
			const fileName = `${Date.now()}.${fileExt}`;

			console.log('File Name: ', fileName);

			const { error: uploadError } = await supabase.storage
				.from('event-banners')
				.upload(fileName, bannerFile, {
					upsert: true, // Overwrite if the file exists
				});

			if (uploadError) {
				console.error('Error uploading image:', uploadError);
				throw new Error('Failed to upload image.');
			}

			// Get the public URL of the uploaded image
			const { data: publicUrlData } = supabase.storage
				.from('event-banners')
				.getPublicUrl(fileName);

			if (publicUrlData) {
				imageUrl = publicUrlData.publicUrl;
			}

			else {
				imageUrl = await supabase.from('events').select('image_url').eq('id', params.eventid).single()
			}
		}

		const { error: updatedDataError } = await supabase.from('events').update({
			title: eventName,
			short_description: shortDescription,
			long_description: longDescription,
			long_description_arabic: longDescriptionArabic,
			start_date: StartDate,
			end_date: endDate,
			start_time: startTime,
			end_time: endTime,
			location: location,
			location_url: locationUrl,
			image_url: imageUrl,
			max_registrations: Number(maxRegistrations) || null,
			is_published: isPublishedValue,
		}).eq('id', params.eventid)

		if (updatedDataError) {
			console.log('Error updating event: ', updatedDataError);
			error(400, { message: 'Failed to update event.' });
		}

		redirect(303, `/private/events/${params.eventid}`);

	},


	// Delete Event
	delete: async ({ request, locals: { supabase, safeGetSession, }, params }) => {
		console.log('Delete Action called..')
		// console.log('Params from Update Action: ', params);
		const { session } = await safeGetSession();
		if (!session) {
			redirect(303, '/');
		}


		// const formData = await request.formData();
		// const ev = formData.get('eventName') as string;


		// console.log('Event Form Data: ', formData);
		const { error: deleteDataError } = await supabase
			.from('events')
			.delete()
			.eq('id', params.eventid)


		if (deleteDataError) {
			console.log('Error deleting event: ', deleteDataError);
		}
		redirect(303, '/private/events');

	}
} satisfies Actions;


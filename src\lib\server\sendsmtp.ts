// import {
//   SMTP_HOST,
//   SMTP_PORT,
//   SMTP_USER,
//   SMTP_PASS,
// } from '$env/static/private';

// import nodemailer from 'nodemailer';



// // Configure your email transporter
// const transporter = nodemailer.createTransport({
//   host: SMTP_HOST,
//   port: SMTP_PORT,
//   auth: {
//     user: SMTP_USER,
//     pass: SMTP_PASS
//   }
// });

export const sendEmailSMTP = async (fname: string, lname: string, email: string, qrLink: string) => {
    console.log('Send Email Function via SMTP Started')

    //   const info = await transporter.sendMail({
    //     from: 'Tickets <<EMAIL>>',
    //     to: `${fname} ${lname} <${email}>`,
    //     bcc: '<EMAIL>',
    //     subject: 'Ticket Confirmation',
    //     html: emailHtmlTemplate(fname, lname, qrLink),
    //     // dsn: {
    //     // 	return: 'headers',
    //     // 	notify: ['failure', 'delay', 'success'],
    //     // },
    //     // attachments: [
    //     //   {   // use URL as an attachment
    //     //     filename: 'invite.ics',
    //     //     path: `https://qtghjrdnxxketsquursz.supabase.co/storage/v1/object/public/other/METCInvite.ics`
    //     //   },
    //     // ]
    //   });

    //   console.log('From Transporter: ', info)
    //   // console.log('accepted: ', info.accepted)


    //   return info

}



// function emailHtmlTemplate(fname: string, lname: string, qrLink: string) {

//   const htmlContent = `
//     <!DOCTYPE html>
//     <html>
//       <head>
//         <meta charset="UTF-8">
//         <title>Ticket Confirmation</title>
//       </head>
//       <body>
//         <h1>Ticket Confirmation</h1>
//         <p>Hi ${fname} ${lname},</p>
//         <p>Thank you for your interest in the Upper Canada College of Arts and Sciences (UCCAS) at the University of Toronto. We have received your request to attend the UCCAS Summer School.</p>
//         <p>Please find the QR code below to scan with your phone to access the event:</p>
//         <img src="${qrLink}" alt="QR Code" width="200" height="200">
//         <p>If you have any questions or concerns, please contact <NAME_EMAIL>.</p>
//         <p>Best regards,</p>
//         <p>The Upper Canada College of Arts and Sciences (UCCAS) at the University of Toronto</p>
//       </body>
//       </html>

//     `


//   return htmlContent

// }
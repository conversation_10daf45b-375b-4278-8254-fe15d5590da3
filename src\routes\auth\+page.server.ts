import { redirect } from '@sveltejs/kit';
import type { Actions } from './$types';

export const actions: Actions = {
	signup: async ({ request, locals: { supabase } }) => {
		const formData = await request.formData();
		const email = formData.get('email') as string;
		const password = formData.get('password') as string;

		// const { error } = await supabase.auth.signUp({ email, password });
		// if (error) {
		// 	console.error(error);
		// 	redirect(303, '/auth/error');
		// } else {
		// 	const { data, error } = await supabase.from('admins').insert({
		// 		email: email,
		// 	});
		// 	if (error) {
		// 		console.error('Error from User Creation: ', error);
		// 		redirect(303, '/auth/error');
		// 	}
		// 	console.log('User Created');
		// 	redirect(303, '/private/events');
		// }

		// Sign up the user
		const { data: authData, error: signUpError } = await supabase.auth.signUp({
			email,
			password,
		});

		if (signUpError || !authData.user) {
			console.error('Signup error:', signUpError);
			throw redirect(303, '/auth/error');
		}

		// Get the user's UUID from the auth response
		const userId = authData.user.id;
		console.log('User ID:', userId);

		// Insert into admins table using the UUID
		const { error: adminError } = await supabase
			.from('admins')
			.insert({
				auth_id: userId,  // This should match your foreign key column name
				email: email,
				// Add any other admin fields you need
			});

		if (adminError) {
			console.error('Error creating admin record:', adminError);

			// Optionally: Delete the auth user if admin creation fails
			await supabase.auth.admin.deleteUser(userId);

			redirect(303, '/auth/error');
		}

		console.log('User and admin record created successfully');
		redirect(303, '/private/events');
	},


	login: async ({ request, locals: { supabase } }) => {
		const formData = await request.formData();
		const email = formData.get('email') as string;
		const password = formData.get('password') as string;

		const { error } = await supabase.auth.signInWithPassword({ email, password });
		if (error) {
			console.error(error);
			redirect(303, '/auth/error');
		} else {
			redirect(303, '/private/events');
		}
	}
};

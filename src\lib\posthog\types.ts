// PostHog integration types for SvelteKit

import type { PostHog } from 'posthog-js';

// Re-export common types from app.d.ts
export type {
	PostHogEventProperties,
	PostHogUserProperties,
	PostHogErrorContext,
	PostHogFeatureFlag,
	PostHogFeatureFlags
} from '../../app.d.ts';

// PostHog configuration types
export interface PostHogInitOptions {
	api_host?: string;
	autocapture?: boolean;
	capture_pageview?: boolean;
	capture_pageleave?: boolean;
	cross_subdomain_cookie?: boolean;
	persistence?: 'localStorage' | 'cookie' | 'memory';
	persistence_name?: string;
	cookie_name?: string;
	cookie_expiration?: number;
	disable_session_recording?: boolean;
	session_recording?: {
		recordCrossOriginIframes?: boolean;
		recordCanvas?: boolean;
		recordPerformance?: boolean;
		maskAllInputs?: boolean;
		maskInputOptions?: {
			password?: boolean;
			email?: boolean;
			[key: string]: boolean | undefined;
		};
	};
	capture_heatmaps?: boolean;
	opt_out_capturing_by_default?: boolean;
	respect_dnt?: boolean;
	debug?: boolean;
	loaded?: (posthog: PostHog) => void;
	[key: string]: any;
}

// Store types
export interface PostHogUser {
	id?: string;
	email?: string;
	name?: string;
	properties?: Record<string, any>;
}

export interface PostHogAnalyticsConsent {
	granted: boolean;
	timestamp?: Date;
}

export interface PostHogEventQueueItem {
	event: string;
	properties?: Record<string, any>;
	timestamp: Date;
}

// Event tracking types
export type PostHogEventName = 
	| 'page_view'
	| 'button_click'
	| 'form_submit'
	| 'error_occurred'
	| 'feature_used'
	| 'user_action'
	| 'user_login'
	| 'user_logout'
	| 'user_signup'
	| 'product_viewed'
	| 'product_added_to_cart'
	| 'purchase_completed'
	| 'search_performed'
	| 'file_downloaded'
	| 'video_played'
	| 'performance_issue'
	| 'high_memory_usage'
	| string; // Allow custom event names

// Common event property interfaces
export interface PageViewProperties extends PostHogEventProperties {
	page_name?: string;
	page_category?: string;
	page_type?: string;
	url?: string;
	referrer?: string;
}

export interface ButtonClickProperties extends PostHogEventProperties {
	button_name: string;
	button_type?: string;
	button_variant?: string;
	source?: string;
	destination?: string;
}

export interface FormSubmitProperties extends PostHogEventProperties {
	form_name: string;
	form_type?: string;
	field_count?: number;
	validation_errors?: string[];
}

export interface ErrorProperties extends PostHogEventProperties {
	error_type: string;
	error_message: string;
	error_stack?: string;
	error_url?: string;
	error_line?: number;
	error_column?: number;
	component?: string;
	action?: string;
}

export interface FeatureUsageProperties extends PostHogEventProperties {
	feature_name: string;
	feature_category?: string;
	feature_enabled?: boolean;
	user_type?: 'authenticated' | 'anonymous';
}

export interface UserActionProperties extends PostHogEventProperties {
	action: string;
	action_category?: string;
	method?: string;
	success?: boolean;
}

export interface EcommerceProperties extends PostHogEventProperties {
	product_id?: string;
	product_name?: string;
	product_category?: string;
	product_price?: number;
	currency?: string;
	quantity?: number;
	order_id?: string;
	order_total?: number;
}

// Test result types
export interface PostHogTestResult {
	test: string;
	passed: boolean;
	message: string;
	details?: any;
}

export interface PostHogTestSuite {
	name: string;
	results: PostHogTestResult[];
	passed: boolean;
	totalTests: number;
	passedTests: number;
}

// Error tracking specific types
export type ErrorType = 
	| 'javascript_error'
	| 'unhandled_promise_rejection'
	| 'network_error'
	| 'user_action_error'
	| 'component_error'
	| 'api_error';

export interface ErrorContext {
	type: ErrorType;
	message: string;
	stack?: string;
	url?: string;
	lineNumber?: number;
	columnNumber?: number;
	userAgent?: string;
	timestamp: Date;
	userId?: string;
	sessionId?: string;
	additionalData?: Record<string, any>;
}

// Privacy and consent types
export interface PrivacySettings {
	optedOut: boolean;
	sessionReplayEnabled: boolean;
	cookiesAccepted: boolean;
	dataProcessingConsent: boolean;
}

// Feature flag configuration types
export interface FeatureFlagConfig {
	[flagName: string]: {
		enabled: boolean;
		variants?: string[];
		rolloutPercentage?: number;
		conditions?: Record<string, any>;
	};
}

// Analytics dashboard types
export interface AnalyticsDashboardData {
	totalEvents: number;
	uniqueUsers: number;
	sessionCount: number;
	averageSessionDuration: number;
	topEvents: Array<{
		name: string;
		count: number;
	}>;
	topPages: Array<{
		path: string;
		views: number;
	}>;
	errorRate: number;
	conversionRate?: number;
}

// Performance monitoring types
export interface PerformanceMetrics {
	pageLoadTime: number;
	firstContentfulPaint: number;
	largestContentfulPaint: number;
	cumulativeLayoutShift: number;
	firstInputDelay: number;
	memoryUsage?: {
		used: number;
		total: number;
		limit: number;
	};
}

// A/B testing types
export interface ABTestVariant {
	name: string;
	weight: number;
	config?: Record<string, any>;
}

export interface ABTestConfig {
	name: string;
	variants: ABTestVariant[];
	trafficAllocation: number;
	targetingRules?: Record<string, any>;
}

// Utility types
export type PostHogEventHandler<T extends PostHogEventProperties = PostHogEventProperties> = (
	eventName: PostHogEventName,
	properties?: T
) => void;

export type PostHogUserIdentifier = string | number;

export type PostHogCallback = () => void;

export type PostHogAsyncCallback = () => Promise<void>;

// Component prop types for PostHog-related components
export interface ErrorBoundaryProps {
	children: any;
	fallback?: any;
	onError?: (error: Error, errorInfo?: any) => void;
	componentName?: string;
}

export interface AnalyticsProviderProps {
	children: any;
	config?: Partial<PostHogInitOptions>;
	onInitialized?: PostHogCallback;
	onError?: (error: Error) => void;
}

// Hook return types (for future React-style hooks if needed)
export interface UsePostHogReturn {
	posthog: PostHog | null;
	isLoaded: boolean;
	error: string | null;
	trackEvent: PostHogEventHandler;
	identifyUser: (userId: PostHogUserIdentifier, properties?: PostHogUserProperties) => void;
	resetUser: PostHogCallback;
	getFeatureFlag: (flagName: string) => PostHogFeatureFlag;
	isFeatureEnabled: (flagName: string) => boolean;
}

// Configuration validation types
export interface PostHogConfigValidation {
	isValid: boolean;
	errors: string[];
	warnings: string[];
}

// Export utility type helpers
export type RequiredPostHogConfig = Required<Pick<PostHogInitOptions, 'api_host'>>;
export type OptionalPostHogConfig = Partial<Omit<PostHogInitOptions, 'api_host'>>;

// Event tracking function signatures
export type TrackEventFunction = (
	eventName: PostHogEventName,
	properties?: PostHogEventProperties,
	options?: {
		timestamp?: Date;
		sendImmediately?: boolean;
	}
) => void;

export type TrackPageViewFunction = (
	pageName?: string,
	properties?: PageViewProperties
) => void;

export type TrackErrorFunction = (
	error: Error,
	context?: Record<string, any>
) => void;

# Flexible Forms Integration Guide

This guide walks you through integrating the flexible forms feature into your existing event management system.

## Prerequisites

1. Ensure you have the latest database schema
2. Install any missing dependencies
3. Backup your existing data

## Step-by-Step Integration

### 1. Database Setup

Run the database migration to create the new tables:

```sql
-- Execute the migration script
\i database/migrations/001_create_flexible_forms_tables.sql
```

### 2. Initialize Existing Events

For existing events, run the migration script to create standard fields:

```bash
# Run the migration script
node scripts/migrate-existing-events.ts
```

### 3. Update Event Creation Workflow

When creating new events, standard fields are automatically initialized. The event creation process in `src/routes/private/events/create/+page.server.ts` should call:

```typescript
import { initializeStandardFields } from '$lib/server/formFields';

// After creating the event
const result = await initializeStandardFields(eventId);
```

### 4. Admin Interface Integration

The form builder is accessible via the event dashboard:
- Navigate to `/private/events/{eventId}/form-builder`
- The navigation has been updated to include the "Form Builder" button

### 5. Registration Form Integration

#### Option A: Replace Existing Form (Recommended)
Replace the content of `src/routes/[slug]/+page.svelte` with the dynamic form:

```svelte
<!-- Replace the existing form section with: -->
<DynamicRegistrationForm 
  eventId={data.event.id} 
  formFields={data.formFields}
  formData={data.form}
/>
```

#### Option B: Gradual Migration
Keep both forms and use a feature flag to switch between them:

```svelte
{#if data.formFields && data.formFields.length > 0}
  <DynamicRegistrationForm {...props} />
{:else}
  <!-- Existing hardcoded form -->
{/if}
```

### 6. Data Migration

Existing guest data can be migrated to the new form responses structure:

```sql
-- Example migration for existing guest data
INSERT INTO form_responses (event_id, guest_id, field_key, field_value)
SELECT 
  event_id, 
  id as guest_id,
  'first_name' as field_key,
  first_name as field_value
FROM guests 
WHERE first_name IS NOT NULL;

-- Repeat for other standard fields...
```

## Testing the Integration

### 1. Test Form Builder
1. Create a new event
2. Navigate to Form Builder
3. Toggle standard fields on/off
4. Create a custom field
5. Preview the form

### 2. Test Registration
1. Visit the event registration page
2. Verify dynamic fields are displayed
3. Test form validation
4. Submit a registration
5. Verify data is saved correctly

### 3. Test API Endpoints
Use the test endpoint to verify functionality:

```bash
# Test field initialization
curl -X POST /api/test-form-fields \
  -H "Content-Type: application/json" \
  -d '{"action": "initialize", "eventId": "your-event-id"}'

# Test field retrieval
curl -X POST /api/test-form-fields \
  -H "Content-Type: application/json" \
  -d '{"action": "get-fields", "eventId": "your-event-id"}'
```

## Troubleshooting

### Common Issues

#### 1. Form Fields Not Loading
- Check if standard fields are initialized for the event
- Verify API endpoints are accessible
- Check browser console for errors

#### 2. Validation Errors
- Ensure Zod schemas are generated correctly
- Check field validation rules
- Verify required fields are properly marked

#### 3. Database Errors
- Ensure migration scripts have been run
- Check database permissions
- Verify foreign key constraints

### Debug Mode

Enable debug logging by setting environment variables:

```bash
DEBUG_FORMS=true
LOG_LEVEL=debug
```

## Performance Considerations

### Database Optimization
- Form fields are cached per event
- Indexes are created on frequently queried columns
- Consider implementing Redis caching for high-traffic events

### Frontend Optimization
- Form fields are loaded once per page load
- Validation is debounced to reduce server calls
- Consider lazy loading for large forms

## Security Checklist

- [ ] RLS policies are enabled on new tables
- [ ] Admin-only access to form configuration
- [ ] Field keys are sanitized
- [ ] Validation rules are applied server-side
- [ ] CSRF protection is enabled
- [ ] Input sanitization is implemented

## Rollback Plan

If issues arise, you can rollback by:

1. Reverting to the previous registration form
2. Disabling the form builder interface
3. Using the old hardcoded form schema

The system is designed to be backward compatible, so existing functionality should continue to work.

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review the implementation documentation
3. Test with the provided API endpoints
4. Check database logs for errors

## Next Steps

After successful integration:
1. Train admin users on the form builder
2. Migrate existing events gradually
3. Monitor performance and user feedback
4. Consider implementing advanced features like conditional logic

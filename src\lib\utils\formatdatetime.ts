export function formatDate(date: string) {
    const dateObject = new Date(date);
    const options: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    return dateObject.toLocaleDateString('en-US', options);
}

export function formatTime(time: string) {
    const timeObject = new Date(`2000-01-01T${time}`); // Provide a dummy date
    const options: Intl.DateTimeFormatOptions = {
        hour: 'numeric',
        minute: 'numeric',
        hour12: true // This option enables AM/PM
    };
    return timeObject.toLocaleTimeString('en-US', options);
}
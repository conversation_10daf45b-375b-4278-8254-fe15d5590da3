import type { PageServerLoad } from './$types';

export const load = (async ({ locals: { supabase } }) => {

    const { data: scanners, error } = await supabase
        .from('scanners')
        .select('*')
        .order('created_at', { ascending: false });

    if (error) {
        console.error('Error fetching scanners:', error);
        return { scanners: [] };
    }
    return { scanners: scanners || [] };
}) satisfies PageServerLoad;
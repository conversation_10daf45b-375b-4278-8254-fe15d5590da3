-- Migration: Event Management Enhancements
-- Description: Ensure proper defaults and indexes for event publishing, visibility, and registration management
-- Date: 2025-01-25

-- Update existing events to have proper default values for new functionality
-- This ensures backward compatibility

-- Set default values for events that don't have these fields set
UPDATE events 
SET 
    is_published = COALESCE(is_published, true),
    is_private = COALESCE(is_private, false),
    registration_open = COALESCE(registration_open, true),
    waiting_list = COALESCE(waiting_list, true),
    auto_approve_waitlist = COALESCE(auto_approve_waitlist, false)
WHERE 
    is_published IS NULL 
    OR is_private IS NULL 
    OR registration_open IS NULL 
    OR waiting_list IS NULL 
    OR auto_approve_waitlist IS NULL;

-- Add indexes for better query performance on public event listings
CREATE INDEX IF NOT EXISTS idx_events_public_listing 
ON events (is_published, is_private, start_date) 
WHERE is_published = true AND is_private = false;

-- Add index for event slug lookups
CREATE INDEX IF NOT EXISTS idx_events_slug 
ON events (title_slug) 
WHERE is_published = true;

-- Add index for admin event management
CREATE INDEX IF NOT EXISTS idx_events_admin_management 
ON events (created_by, is_published, created_at);

-- Add index for registration status checks
CREATE INDEX IF NOT EXISTS idx_events_registration_status 
ON events (registration_open, start_date, start_time) 
WHERE is_published = true;

-- Add index for private events with access codes
CREATE INDEX IF NOT EXISTS idx_events_private_access 
ON events (is_private, active_code) 
WHERE is_private = true AND active_code IS NOT NULL;

-- Add index for guest status filtering (for waiting list management)
CREATE INDEX IF NOT EXISTS idx_guests_status_event 
ON guests (event_id, status, created_at);

-- Add index for registration count queries
CREATE INDEX IF NOT EXISTS idx_guests_registration_count 
ON guests (event_id, status) 
WHERE status IN ('registered', 'imported', 'waiting');

-- Add comments to document the new functionality
COMMENT ON COLUMN events.is_published IS 'Whether the event is published and visible to the public';
COMMENT ON COLUMN events.is_private IS 'Whether the event requires an access code to view';
COMMENT ON COLUMN events.active_code IS 'Access code required for private events';
COMMENT ON COLUMN events.registration_open IS 'Whether registration is currently open (can be auto-closed at event start)';
COMMENT ON COLUMN events.waiting_list IS 'Whether to enable waiting list when event is full';
COMMENT ON COLUMN events.auto_approve_waitlist IS 'Whether to automatically approve waiting list registrations';
COMMENT ON COLUMN events.max_registrations IS 'Maximum number of registrations allowed (NULL = unlimited)';

-- Create a view for public events (commonly queried)
CREATE OR REPLACE VIEW public_events AS
SELECT 
    id,
    title,
    title_slug,
    short_description,
    long_description,
    image_url,
    start_date,
    start_time,
    end_date,
    end_time,
    location,
    location_url,
    max_registrations,
    registration_open,
    waiting_list,
    created_at
FROM events 
WHERE is_published = true 
  AND is_private = false 
  AND start_date >= CURRENT_DATE;

-- Create a function to automatically close registration at event start time
CREATE OR REPLACE FUNCTION auto_close_registration()
RETURNS void AS $$
BEGIN
    UPDATE events 
    SET registration_open = false 
    WHERE registration_open = true 
      AND is_published = true 
      AND CONCAT(start_date, ' ', start_time)::timestamp <= NOW();
END;
$$ LANGUAGE plpgsql;

-- Create a function to get registration status with capacity info
CREATE OR REPLACE FUNCTION get_registration_status(event_uuid UUID)
RETURNS TABLE (
    is_open boolean,
    reason text,
    current_count integer,
    max_count integer,
    is_full boolean,
    waiting_list_enabled boolean
) AS $$
DECLARE
    event_record RECORD;
    reg_count integer;
BEGIN
    -- Get event data
    SELECT * INTO event_record 
    FROM events 
    WHERE id = event_uuid;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'Event not found'::text, 0, 0, true, false;
        RETURN;
    END IF;
    
    -- Check if published
    IF NOT event_record.is_published THEN
        RETURN QUERY SELECT false, 'Event is not published'::text, 0, 0, true, false;
        RETURN;
    END IF;
    
    -- Check if registration is manually closed
    IF NOT event_record.registration_open THEN
        RETURN QUERY SELECT false, 'Registration is closed'::text, 0, 0, true, false;
        RETURN;
    END IF;
    
    -- Check if event has started
    IF CONCAT(event_record.start_date, ' ', event_record.start_time)::timestamp <= NOW() THEN
        -- Auto-close registration
        UPDATE events SET registration_open = false WHERE id = event_uuid;
        RETURN QUERY SELECT false, 'Registration closed - event has started'::text, 0, 0, true, false;
        RETURN;
    END IF;
    
    -- Count current registrations
    SELECT COUNT(*) INTO reg_count 
    FROM guests 
    WHERE event_id = event_uuid 
      AND status IN ('registered', 'imported');
    
    -- Check capacity
    IF event_record.max_registrations IS NOT NULL AND reg_count >= event_record.max_registrations THEN
        RETURN QUERY SELECT 
            event_record.waiting_list, 
            CASE WHEN event_record.waiting_list THEN 'Event is full - joining waiting list' ELSE 'Event is full' END::text,
            reg_count,
            event_record.max_registrations,
            true,
            COALESCE(event_record.waiting_list, false);
        RETURN;
    END IF;
    
    -- Registration is open
    RETURN QUERY SELECT 
        true, 
        'Registration is open'::text,
        reg_count,
        event_record.max_registrations,
        false,
        COALESCE(event_record.waiting_list, false);
END;
$$ LANGUAGE plpgsql;

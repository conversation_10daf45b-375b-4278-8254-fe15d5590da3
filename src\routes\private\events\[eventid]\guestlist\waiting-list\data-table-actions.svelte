<script lang="ts">
	import Ellipsis from 'lucide-svelte/icons/ellipsis';
	import { Button } from '$lib/components/ui/button/index.js';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
	import { supabase } from '$lib/db/supabaseClient';

	import toast, { Toaster } from 'svelte-hot-french-toast';

	let { id }: { id: string } = $props();

	async function approveGuest(id) {
		console.log('Approving guest: ', id);
		const { data: approveData, error: approveError } = await supabase
			.from('guests')
			.update({ status: 'Approved' })
			.eq('id', id);
		if (approveError) {
			console.error('Error approving guest: ', approveError);
		}
		console.log('Guest approved: ', approveData);
	}

	async function rejectGuestWithEmail(id) {
		console.log('Rejecting guest with email: ', id);
		const { data: rejectData, error: rejectError } = await supabase
			.from('guests')
			.update({ status: 'Rejected with Email' })
			.eq('id', id);
		if (rejectError) {
			console.error('Error rejecting guest with email: ', rejectError);
		}
		console.log('Guest rejected with email: ', rejectData);
	}

	async function rejectGeustWithoutEmail(id) {
		console.log('Rejecting guest without email: ', id);
		const { data: rejectData, error: rejectError } = await supabase
			.from('guests')
			.update({ status: 'Rejected without Email' })
			.eq('id', id);
		if (rejectError) {
			console.error('Error rejecting guest without email: ', rejectError);
		}
		console.log('Guest rejected without email: ', rejectData);
	}
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger>
		{#snippet child({ props })}
			<Button {...props} variant="ghost" size="icon" class="relative size-8 p-0">
				<span class="sr-only">Open menu</span>
				<Ellipsis class="size-4" />
			</Button>
		{/snippet}
	</DropdownMenu.Trigger>
	<DropdownMenu.Content>
		<DropdownMenu.Item onclick={() => approveGuest(id)}>Approve Guest</DropdownMenu.Item>
		<DropdownMenu.Item onclick={() => rejectGuestWithEmail(id)}
			>Reject Guest with Email</DropdownMenu.Item
		>
		<DropdownMenu.Item onclick={() => rejectGeustWithoutEmail(id)}
			>Reject Guest without Email</DropdownMenu.Item
		>
	</DropdownMenu.Content>
</DropdownMenu.Root>

<Toaster />

<script lang="ts">
	import * as Card from '$lib/components/ui/card/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { Switch } from '$lib/components/ui/switch/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { enhance } from '$app/forms';
	import type { PageData } from './$types';

	export let data: PageData;

	// Reactive variables for form state
	let isPublished = data.event.is_published ?? false;
	let isPrivate = data.event.is_private ?? false;
	let registrationOpen = data.event.registration_open ?? true;
	let waitingList = data.event.waiting_list ?? true;
	let autoApproveWaitlist = data.event.auto_approve_waitlist ?? false;
	let maxRegistrations = data.event.max_registrations?.toString() ?? '';
	let activeCode = data.event.active_code ?? '';
</script>

<div class="space-y-8 p-8">
	<form method="POST" action="?/updateSettings" use:enhance class="space-y-8">
		<!-- Publishing and Visibility Settings -->
		<Card.Root>
			<Card.Header>
				<Card.Title>Publishing & Visibility</Card.Title>
				<Card.Description>Control who can see and access your event.</Card.Description>
			</Card.Header>
			<Card.Content class="space-y-6">
				<!-- Published Status -->
				<div class="flex items-center justify-between">
					<div>
						<Label for="is_published">Event Published</Label>
						<p class="text-sm text-muted-foreground">Make event visible to the public</p>
					</div>
					<Switch id="is_published" name="is_published" bind:checked={isPublished} />
				</div>

				<!-- Private Event -->
				<div class="flex items-center justify-between">
					<div>
						<Label for="is_private">Private Event</Label>
						<p class="text-sm text-muted-foreground">Require access code to view event</p>
					</div>
					<Switch id="is_private" name="is_private" bind:checked={isPrivate} />
				</div>

				<!-- Access Code (only show if private) -->
				{#if isPrivate}
					<div class="space-y-2">
						<Label for="active_code">Access Code</Label>
						<Input
							id="active_code"
							name="active_code"
							type="text"
							bind:value={activeCode}
							placeholder="Enter access code for private event"
						/>
						<p class="text-sm text-muted-foreground">
							Users will need this code to access the private event
						</p>
					</div>
				{/if}
			</Card.Content>
		</Card.Root>

		<!-- Registration Settings -->
		<Card.Root>
			<Card.Header>
				<Card.Title>Registration Settings</Card.Title>
				<Card.Description>Manage event registration and capacity.</Card.Description>
			</Card.Header>
			<Card.Content class="space-y-6">
				<!-- Registration Open -->
				<div class="flex items-center justify-between">
					<div>
						<Label for="registration_open">Registration Open</Label>
						<p class="text-sm text-muted-foreground">Allow new registrations</p>
					</div>
					<Switch id="registration_open" name="registration_open" bind:checked={registrationOpen} />
				</div>

				<!-- Maximum Registrations -->
				<div class="space-y-2">
					<Label for="max_registrations">Maximum Registrations</Label>
					<Input
						id="max_registrations"
						name="max_registrations"
						type="number"
						bind:value={maxRegistrations}
						placeholder="Leave empty for unlimited"
						min="1"
					/>
					<p class="text-sm text-muted-foreground">Leave empty for unlimited registrations</p>
				</div>

				<!-- Waiting List -->
				<div class="flex items-center justify-between">
					<div>
						<Label for="waiting_list">Enable Waiting List</Label>
						<p class="text-sm text-muted-foreground">Allow registrations when event is full</p>
					</div>
					<Switch id="waiting_list" name="waiting_list" bind:checked={waitingList} />
				</div>

				<!-- Auto Approve Waiting List -->
				{#if waitingList}
					<div class="flex items-center justify-between">
						<div>
							<Label for="auto_approve_waitlist">Auto-approve Waiting List</Label>
							<p class="text-sm text-muted-foreground">
								Automatically approve waiting list registrations
							</p>
						</div>
						<Switch
							id="auto_approve_waitlist"
							name="auto_approve_waitlist"
							bind:checked={autoApproveWaitlist}
						/>
					</div>
				{/if}
			</Card.Content>
		</Card.Root>

		<!-- Save Button -->
		<div class="flex justify-end">
			<Button type="submit" class="w-full sm:w-auto">Save Settings</Button>
		</div>
	</form>

	<!-- Admin Actions -->
	<div class="flex justify-start p-8 pt-0">
		<form method="POST" action="?/reopenRegistration" use:enhance>
			<Button type="submit" variant="outline" class="w-full sm:w-auto">
				Force Reopen Registration
			</Button>
		</form>
	</div>
</div>

import type { LayoutServerLoad } from './$types';

export const load: LayoutServerLoad = async ({ params, locals: { supabase } }) => {
    const { data: eventData, error } = await supabase
        .from('events')
        .select('title')
        .eq('id', params.eventid)
        .single();

    if (error) {
        console.error('Error fetching event:', error);
        return {
            eventTitle: 'Unknown Event'
        };
    }

    return {
        eventTitle: eventData.title,
        params
    };
};
// Server-side form response management functions

import { supabaseAdmin } from '$lib/server/db/supabaseAdmin';
import type { 
  FormResponse, 
  GuestFormResponse, 
  DynamicFormData, 
  FormSubmissionData,
  FormField,
  FormValidationResult,
  FormValidationError
} from '$lib/types/formFields';

/**
 * Save form responses for a guest
 */
export async function saveFormResponses(submissionData: FormSubmissionData): Promise<{ success: boolean; guestId?: number; error?: string }> {
  try {
    const { eventId, responses } = submissionData;
    let { guestId } = submissionData;

    // If no guestId provided, we need to create a guest record first
    if (!guestId) {
      const { data: guestData, error: guestError } = await supabaseAdmin
        .from('guests')
        .insert({
          event_id: eventId,
          // We'll populate basic fields from responses if they exist
          first_name: responses.first_name as string || null,
          last_name: responses.last_name as string || null,
          email: responses.email as string || null,
          mobile: responses.mobile as string || null,
          organization: responses.organization as string || null,
        })
        .select('id')
        .single();

      if (guestError) {
        console.error('Error creating guest record:', guestError);
        return { success: false, error: guestError.message };
      }

      guestId = guestData.id;
    }

    // Prepare form responses for insertion
    const formResponses = Object.entries(responses).map(([fieldKey, fieldValue]) => ({
      event_id: eventId,
      guest_id: guestId!,
      field_key: fieldKey,
      field_value: Array.isArray(fieldValue) ? fieldValue.join(',') : String(fieldValue)
    }));

    // Insert form responses
    const { error: responsesError } = await supabaseAdmin
      .from('form_responses')
      .upsert(formResponses, {
        onConflict: 'guest_id,field_key'
      });

    if (responsesError) {
      console.error('Error saving form responses:', responsesError);
      return { success: false, error: responsesError.message };
    }

    return { success: true, guestId };
  } catch (error) {
    console.error('Error in saveFormResponses:', error);
    return { success: false, error: 'Failed to save form responses' };
  }
}

/**
 * Get form responses for a specific guest
 */
export async function getGuestFormResponses(guestId: number): Promise<{ data: GuestFormResponse[] | null; error?: string }> {
  try {
    const { data, error } = await supabaseAdmin.rpc('get_guest_form_responses', {
      p_guest_id: guestId
    });

    if (error) {
      console.error('Error fetching guest form responses:', error);
      return { data: null, error: error.message };
    }

    const responses: GuestFormResponse[] = data.map((response: any) => ({
      fieldKey: response.field_key,
      fieldLabel: response.field_label,
      fieldType: response.field_type,
      fieldValue: response.field_value || ''
    }));

    return { data: responses };
  } catch (error) {
    console.error('Error in getGuestFormResponses:', error);
    return { data: null, error: 'Failed to fetch guest form responses' };
  }
}

/**
 * Get all form responses for an event (for admin dashboard)
 */
export async function getEventFormResponses(eventId: string): Promise<{ data: any[] | null; error?: string }> {
  try {
    // Get all guests for the event with their form responses
    const { data: guests, error: guestsError } = await supabaseAdmin
      .from('guests')
      .select(`
        id,
        created_at,
        status,
        form_responses (
          field_key,
          field_value
        )
      `)
      .eq('event_id', eventId);

    if (guestsError) {
      console.error('Error fetching event form responses:', guestsError);
      return { data: null, error: guestsError.message };
    }

    // Transform the data to a more usable format
    const transformedData = guests.map(guest => {
      const responses: { [key: string]: string } = {};
      
      if (guest.form_responses) {
        guest.form_responses.forEach((response: any) => {
          responses[response.field_key] = response.field_value || '';
        });
      }

      return {
        guestId: guest.id,
        createdAt: guest.created_at,
        status: guest.status,
        responses
      };
    });

    return { data: transformedData };
  } catch (error) {
    console.error('Error in getEventFormResponses:', error);
    return { data: null, error: 'Failed to fetch event form responses' };
  }
}

/**
 * Update form responses for a guest
 */
export async function updateGuestFormResponses(guestId: number, responses: DynamicFormData): Promise<{ success: boolean; error?: string }> {
  try {
    // Get the event ID for this guest
    const { data: guestData, error: guestError } = await supabaseAdmin
      .from('guests')
      .select('event_id')
      .eq('id', guestId)
      .single();

    if (guestError) {
      console.error('Error fetching guest data:', guestError);
      return { success: false, error: guestError.message };
    }

    const eventId = guestData.event_id;

    // Prepare form responses for upsert
    const formResponses = Object.entries(responses).map(([fieldKey, fieldValue]) => ({
      event_id: eventId,
      guest_id: guestId,
      field_key: fieldKey,
      field_value: Array.isArray(fieldValue) ? fieldValue.join(',') : String(fieldValue)
    }));

    // Upsert form responses
    const { error: responsesError } = await supabaseAdmin
      .from('form_responses')
      .upsert(formResponses, {
        onConflict: 'guest_id,field_key'
      });

    if (responsesError) {
      console.error('Error updating form responses:', responsesError);
      return { success: false, error: responsesError.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in updateGuestFormResponses:', error);
    return { success: false, error: 'Failed to update form responses' };
  }
}

/**
 * Delete form responses for a guest
 */
export async function deleteGuestFormResponses(guestId: number): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabaseAdmin
      .from('form_responses')
      .delete()
      .eq('guest_id', guestId);

    if (error) {
      console.error('Error deleting form responses:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in deleteGuestFormResponses:', error);
    return { success: false, error: 'Failed to delete form responses' };
  }
}

/**
 * Validate form data against field configurations
 */
export async function validateFormData(eventId: string, formData: DynamicFormData): Promise<FormValidationResult> {
  try {
    // Get form fields for validation
    const { data: formFields, error } = await supabaseAdmin
      .from('form_fields')
      .select('*')
      .eq('event_id', eventId)
      .eq('is_enabled', true);

    if (error) {
      console.error('Error fetching form fields for validation:', error);
      return { isValid: false, errors: [{ fieldKey: 'general', message: 'Validation failed' }] };
    }

    const errors: FormValidationError[] = [];

    for (const field of formFields) {
      const fieldValue = formData[field.field_key];
      const fieldLabel = field.field_label;

      // Check required fields
      if (field.is_required && (!fieldValue || fieldValue === '')) {
        errors.push({
          fieldKey: field.field_key,
          message: `${fieldLabel} is required`
        });
        continue;
      }

      // Skip validation if field is empty and not required
      if (!fieldValue || fieldValue === '') {
        continue;
      }

      // Validate based on field type
      if (field.field_type === 'email') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(String(fieldValue))) {
          errors.push({
            fieldKey: field.field_key,
            message: `${fieldLabel} must be a valid email address`
          });
        }
      }

      if (field.field_type === 'phone') {
        const phoneRegex = /^\+?[0-9]{8,14}$/;
        if (!phoneRegex.test(String(fieldValue))) {
          errors.push({
            fieldKey: field.field_key,
            message: `${fieldLabel} must be a valid phone number`
          });
        }
      }

      if (field.field_type === 'url') {
        try {
          new URL(String(fieldValue));
        } catch {
          errors.push({
            fieldKey: field.field_key,
            message: `${fieldLabel} must be a valid URL`
          });
        }
      }

      // Validate against custom validation rules
      if (field.validation_rules) {
        const rules = field.validation_rules;
        const value = String(fieldValue);

        if (rules.minLength && value.length < rules.minLength) {
          errors.push({
            fieldKey: field.field_key,
            message: `${fieldLabel} must be at least ${rules.minLength} characters long`
          });
        }

        if (rules.maxLength && value.length > rules.maxLength) {
          errors.push({
            fieldKey: field.field_key,
            message: `${fieldLabel} must be no more than ${rules.maxLength} characters long`
          });
        }

        if (rules.pattern) {
          const regex = new RegExp(rules.pattern);
          if (!regex.test(value)) {
            errors.push({
              fieldKey: field.field_key,
              message: rules.customMessage || `${fieldLabel} format is invalid`
            });
          }
        }

        if (field.field_type === 'number') {
          const numValue = Number(fieldValue);
          if (isNaN(numValue)) {
            errors.push({
              fieldKey: field.field_key,
              message: `${fieldLabel} must be a valid number`
            });
          } else {
            if (rules.min !== undefined && numValue < rules.min) {
              errors.push({
                fieldKey: field.field_key,
                message: `${fieldLabel} must be at least ${rules.min}`
              });
            }
            if (rules.max !== undefined && numValue > rules.max) {
              errors.push({
                fieldKey: field.field_key,
                message: `${fieldLabel} must be no more than ${rules.max}`
              });
            }
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  } catch (error) {
    console.error('Error in validateFormData:', error);
    return { 
      isValid: false, 
      errors: [{ fieldKey: 'general', message: 'Validation failed due to server error' }] 
    };
  }
}

<script lang="ts">
	import { cn } from '$lib/utils.js';

	interface Props {
		variant?: 'default' | 'destructive';
		class?: string;
		children?: any;
		[key: string]: any;
	}

	let { variant = 'default', class: className, children, ...restProps }: Props = $props();

	function getAlertClasses(variant: string) {
		const baseClasses =
			'relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground';

		if (variant === 'destructive') {
			return `${baseClasses} border-red-500/50 text-red-600 dark:border-red-500 [&>svg]:text-red-600`;
		}

		return `${baseClasses} bg-white text-gray-900 border-gray-200`;
	}
</script>

<div role="alert" class={cn(getAlertClasses(variant), className)} {...restProps}>
	{@render children?.()}
</div>

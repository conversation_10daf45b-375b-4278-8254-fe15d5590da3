<script lang="ts">
	import { onMount } from 'svelte';
	import {
		trackEvent,
		trackButtonClick,
		trackFormSubmit,
		trackFeatureUsage,
		trackUserAction,
		identifyUser,
		setUserProperties,
		logoutUser,
		getFeatureFlag,
		isFeatureEnabled,
		startSessionReplay,
		stopSessionReplay,
		optOutOfTracking,
		optInToTracking,
		hasOptedOut,
		debugPostHog
	} from '$lib/posthog/utils';
	import {
		trackJavaScriptError,
		trackApiError,
		withErrorTracking
	} from '$lib/posthog/error-tracking';
	import { currentUser, sessionReplayEnabled, posthogAvailable } from '$lib/posthog/stores';

	// Example user data
	let userName = $state('');
	let userEmail = $state('');
	let isLoggedIn = $state(false);

	// Example form data
	let feedbackMessage = $state('');
	let rating = $state(5);

	// Feature flag examples
	let newFeatureEnabled = $state(false);
	let buttonVariant = $state('default');

	// Privacy settings
	let hasUserOptedOut = $state(false);

	onMount(() => {
		// Check feature flags
		newFeatureEnabled = isFeatureEnabled('new_feature_toggle');
		buttonVariant = (getFeatureFlag('button_variant') as string) || 'default';
		hasUserOptedOut = hasOptedOut();

		// Track page load
		trackEvent('examples_page_loaded', {
			timestamp: new Date().toISOString(),
			user_agent: navigator.userAgent
		});
	});

	// User identification examples
	const handleLogin = () => {
		if (!userName || !userEmail) return;

		identifyUser(userEmail, {
			name: userName,
			email: userEmail,
			login_method: 'form',
			first_login: !isLoggedIn
		});

		isLoggedIn = true;
		trackUserAction('user_login', { method: 'form' });
	};

	const handleLogout = () => {
		logoutUser();
		isLoggedIn = false;
		userName = '';
		userEmail = '';
		trackUserAction('user_logout');
	};

	const updateUserProfile = () => {
		setUserProperties({
			last_profile_update: new Date().toISOString(),
			profile_completion: 85
		});
		trackUserAction('profile_updated');
	};

	// Event tracking examples
	const handleButtonClick = (buttonName: string) => {
		trackButtonClick(buttonName, {
			variant: buttonVariant,
			timestamp: new Date().toISOString()
		});
	};

	const handleFormSubmit = () => {
		if (!feedbackMessage) return;

		trackFormSubmit('feedback_form', {
			message_length: feedbackMessage.length,
			rating: rating,
			has_user: isLoggedIn
		});

		// Reset form
		feedbackMessage = '';
		rating = 5;
	};

	const trackFeatureClick = (featureName: string) => {
		trackFeatureUsage(featureName, {
			feature_enabled: newFeatureEnabled,
			user_type: isLoggedIn ? 'authenticated' : 'anonymous'
		});
	};

	// Error tracking examples
	const triggerJavaScriptError = () => {
		try {
			// Intentionally cause an error
			throw new Error('This is a test JavaScript error');
		} catch (error) {
			trackJavaScriptError(error as Error, {
				triggered_by: 'user_action',
				test_error: true
			});
		}
	};

	const triggerApiError = async () => {
		try {
			const response = await fetch('/api/nonexistent-endpoint');
			if (!response.ok) {
				trackApiError('/api/nonexistent-endpoint', 'GET', response.status, 'Endpoint not found', {
					test_error: true
				});
			}
		} catch (error) {
			trackApiError('/api/nonexistent-endpoint', 'GET', undefined, (error as Error).message, {
				test_error: true
			});
		}
	};

	// Async function with error tracking
	const exampleAsyncFunction = withErrorTracking(async () => {
		// Simulate async operation
		await new Promise((resolve) => setTimeout(resolve, 1000));

		// Simulate potential error
		if (Math.random() > 0.7) {
			throw new Error('Random async error occurred');
		}

		trackEvent('async_operation_completed');
		return 'Success!';
	}, 'example_async_operation');

	// Session replay controls
	const toggleSessionReplay = () => {
		if ($sessionReplayEnabled) {
			stopSessionReplay();
		} else {
			startSessionReplay();
		}
	};

	// Privacy controls
	const toggleOptOut = () => {
		if (hasUserOptedOut) {
			optInToTracking();
			hasUserOptedOut = false;
		} else {
			optOutOfTracking();
			hasUserOptedOut = true;
		}
	};

	// Custom event examples
	const trackCustomEvent = () => {
		trackEvent('custom_event_example', {
			event_category: 'user_interaction',
			event_value: Math.floor(Math.random() * 100),
			custom_property: 'example_value',
			timestamp: new Date().toISOString()
		});
	};

	const trackEcommerceEvent = () => {
		trackEvent('product_viewed', {
			product_id: 'example-product-123',
			product_name: 'Example Product',
			product_category: 'Electronics',
			product_price: 99.99,
			currency: 'USD'
		});
	};
</script>

<div class="posthog-examples">
	<h1>PostHog Integration Examples</h1>

	<div class="status-section">
		<h2>PostHog Status</h2>
		<p>
			PostHog Available: <span class="status {$posthogAvailable ? 'active' : 'inactive'}"
				>{$posthogAvailable ? 'Yes' : 'No'}</span
			>
		</p>
		<p>
			Session Replay: <span class="status {$sessionReplayEnabled ? 'active' : 'inactive'}"
				>{$sessionReplayEnabled ? 'Enabled' : 'Disabled'}</span
			>
		</p>
		<p>
			Opted Out: <span class="status {hasUserOptedOut ? 'inactive' : 'active'}"
				>{hasUserOptedOut ? 'Yes' : 'No'}</span
			>
		</p>
		<button onclick={debugPostHog} class="debug-btn">Debug PostHog</button>
	</div>

	<div class="section">
		<h2>User Identification</h2>
		<div class="form-group">
			<input bind:value={userName} placeholder="Enter your name" />
			<input bind:value={userEmail} placeholder="Enter your email" type="email" />
			<button onclick={handleLogin} disabled={!userName || !userEmail || isLoggedIn}>
				Login
			</button>
			<button onclick={handleLogout} disabled={!isLoggedIn}> Logout </button>
		</div>
		{#if $currentUser}
			<p>Current User: {$currentUser.name || $currentUser.email || $currentUser.id}</p>
			<button onclick={updateUserProfile}>Update Profile</button>
		{/if}
	</div>

	<div class="section">
		<h2>Event Tracking</h2>
		<div class="button-group">
			<button onclick={() => handleButtonClick('primary_cta')} class="btn-primary">
				Primary CTA
			</button>
			<button onclick={() => handleButtonClick('secondary_cta')} class="btn-secondary">
				Secondary CTA
			</button>
			<button onclick={trackCustomEvent} class="btn-custom"> Track Custom Event </button>
			<button onclick={trackEcommerceEvent} class="btn-ecommerce"> Track Product View </button>
		</div>
	</div>

	<div class="section">
		<h2>Feature Usage Tracking</h2>
		<div class="feature-section">
			<p>
				New Feature Enabled: <span class="status {newFeatureEnabled ? 'active' : 'inactive'}"
					>{newFeatureEnabled ? 'Yes' : 'No'}</span
				>
			</p>
			<p>Button Variant: <span class="variant">{buttonVariant}</span></p>
			<button onclick={() => trackFeatureClick('new_dashboard')} class="feature-btn">
				Use New Dashboard
			</button>
			<button onclick={() => trackFeatureClick('advanced_analytics')} class="feature-btn">
				Use Advanced Analytics
			</button>
		</div>
	</div>

	<div class="section">
		<h2>Form Tracking</h2>
		<form onsubmit={handleFormSubmit} class="feedback-form">
			<textarea bind:value={feedbackMessage} placeholder="Enter your feedback" rows="4"></textarea>
			<div class="rating-group">
				<label>Rating: {rating}/5</label>
				<input type="range" bind:value={rating} min="1" max="5" />
			</div>
			<button type="submit" disabled={!feedbackMessage}>Submit Feedback</button>
		</form>
	</div>

	<div class="section">
		<h2>Error Tracking</h2>
		<div class="button-group">
			<button onclick={triggerJavaScriptError} class="btn-error"> Trigger JS Error </button>
			<button onclick={triggerApiError} class="btn-error"> Trigger API Error </button>
			<button onclick={exampleAsyncFunction} class="btn-async"> Test Async Function </button>
		</div>
	</div>

	<div class="section">
		<h2>Privacy & Session Controls</h2>
		<div class="control-group">
			<button onclick={toggleSessionReplay} class="control-btn">
				{$sessionReplayEnabled ? 'Stop' : 'Start'} Session Replay
			</button>
			<button onclick={toggleOptOut} class="control-btn">
				{hasUserOptedOut ? 'Opt In' : 'Opt Out'} of Tracking
			</button>
		</div>
	</div>
</div>

<style>
	.posthog-examples {
		max-width: 800px;
		margin: 0 auto;
		padding: 2rem;
		font-family: system-ui, sans-serif;
	}

	.section {
		margin-bottom: 2rem;
		padding: 1.5rem;
		border: 1px solid #e5e7eb;
		border-radius: 0.5rem;
		background-color: #f9fafb;
	}

	.status-section {
		background-color: #f0f9ff;
		border-color: #0ea5e9;
	}

	h1,
	h2 {
		margin-bottom: 1rem;
		color: #1f2937;
	}

	.status {
		font-weight: 600;
		padding: 0.25rem 0.5rem;
		border-radius: 0.25rem;
	}

	.status.active {
		background-color: #dcfce7;
		color: #166534;
	}

	.status.inactive {
		background-color: #fef2f2;
		color: #dc2626;
	}

	.form-group,
	.button-group,
	.control-group {
		display: flex;
		gap: 0.5rem;
		flex-wrap: wrap;
		align-items: center;
	}

	input,
	textarea {
		padding: 0.5rem;
		border: 1px solid #d1d5db;
		border-radius: 0.375rem;
		font-size: 0.875rem;
	}

	button {
		padding: 0.5rem 1rem;
		border: none;
		border-radius: 0.375rem;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s;
	}

	button:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	.btn-primary {
		background-color: #3b82f6;
		color: white;
	}

	.btn-secondary {
		background-color: #6b7280;
		color: white;
	}

	.btn-custom {
		background-color: #8b5cf6;
		color: white;
	}

	.btn-ecommerce {
		background-color: #10b981;
		color: white;
	}

	.btn-error {
		background-color: #ef4444;
		color: white;
	}

	.btn-async {
		background-color: #f59e0b;
		color: white;
	}

	.feature-btn {
		background-color: #06b6d4;
		color: white;
	}

	.control-btn {
		background-color: #374151;
		color: white;
	}

	.debug-btn {
		background-color: #1f2937;
		color: white;
	}

	.feedback-form {
		display: flex;
		flex-direction: column;
		gap: 1rem;
	}

	.rating-group {
		display: flex;
		align-items: center;
		gap: 1rem;
	}

	.variant {
		background-color: #ddd6fe;
		color: #5b21b6;
		padding: 0.25rem 0.5rem;
		border-radius: 0.25rem;
		font-weight: 500;
	}
</style>

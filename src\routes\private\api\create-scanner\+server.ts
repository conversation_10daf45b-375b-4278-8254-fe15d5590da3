import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
// import bcrypt from 'bcrypt';

export const POST: RequestHandler = async ({ request, locals: { supabase } }) => {
    try {
        const data = await request.json();

        // Hash the password
        // const saltRounds = 10;
        // const hashedPassword = await bcrypt.hash(data.password, saltRounds);

        // Insert scanner with hashed password
        const { data: scanner, error } = await supabase
            .from('scanners')
            .insert({
                name: data.name,
                username: data.username,
                // password_hash: hashedPassword,
                password_hash: data.password,

                location: data.location,
                mode: data.mode,
                requires_validation: data.requiresValidation,
                gate: data.gate
            })
            .select()
            .single();

        if (error) {
            console.error('Error creating scanner:', error);
            return json({ success: false, error: error.message }, { status: 500 });
        }

        return json({ success: true, scanner });
    } catch (error) {
        console.error('Error in create-scanner endpoint:', error);
        return json({ success: false, error: 'Internal server error' }, { status: 500 });
    }
};
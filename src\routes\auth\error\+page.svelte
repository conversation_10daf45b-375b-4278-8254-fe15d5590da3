<script>
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { analytics } from '$lib/analytics/analytics';

	onMount(() => {
		// Track login failure
		const errorMessage = $page.url.searchParams.get('message') || 'Login failed';
		analytics.trackLoginFailure(errorMessage);
	});
</script>

<div class="flex h-screen items-center justify-center">
	<div class="text-center">
		<h1 class="mb-4 text-2xl font-bold text-red-600">Login Error</h1>
		<p class="mb-4 text-gray-600">There was an error logging you in. Please try again.</p>
		<a href="/auth" class="text-blue-500 hover:underline">Back to Login</a>
	</div>
</div>

<script lang="ts">
	import { page } from '$app/stores';
	import { onMount } from 'svelte';
	import { formatDate, formatTime } from '$lib/utils/formatdatetime.js';
	import Footer from '$lib/components/Footer.svelte';
	import DynamicRegistrationForm from '$lib/components/forms/DynamicRegistrationForm.svelte';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import { Badge } from '$lib/components/ui/badge';
	import { Calendar, MapPin, Users, Clock, Globe } from 'lucide-svelte';
	import Markdown from '$lib/components/Markdown.svelte';

	export let data;

	// Check if this is a preview
	$: isPreview = $page.url.searchParams.has('preview');
	$: eventId = data.event?.id;
	$: formFields = data.formFields || [];

	// Define page metadata
	$: pageTitle = `${data.event?.title} | eHaris Events`;
	$: pageDescription = `Register for ${data.event?.title}`;
	$: siteName = 'eventsdemo.eHaris.com';
	$: title = `${data.event?.title}`;
	$: description = `${data.event?.short_description}`;
	$: image = data.event?.image_url;

	// Event status
	$: isEventFull = data.waitingList;
	$: registrationCount = data.registrationCount || 0;
	$: maxRegistrations = data.event?.max_registrations;
</script>

<svelte:head>
	<title>{pageTitle}</title>
	<meta name="description" content={pageDescription} />
	<meta property="og:title" content={title} />
	<meta property="og:description" content={description} />
	<meta property="og:image" content={image} />
	<meta property="og:url" content={$page.url.href} />
	<meta property="og:type" content="event" />
	<meta property="og:site_name" content={siteName} />
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:title" content={title} />
	<meta name="twitter:description" content={description} />
	<meta name="twitter:image" content={image} />
</svelte:head>

<main class="min-h-screen bg-gray-50">
	<!-- Hero Section -->
	<div class="relative">
		{#if data.event?.image_url}
			<div
				class="relative h-64 bg-cover bg-center md:h-80"
				style="background-image: url('{data.event.image_url}')"
			>
				<div class="absolute inset-0 bg-black bg-opacity-50"></div>
				<div class="absolute inset-0 flex items-center justify-center">
					<div class="px-4 text-center text-white">
						<h1 class="mb-4 text-3xl font-bold md:text-5xl">{data.event.title}</h1>
						<p class="text-lg opacity-90 md:text-xl">{data.event.short_description}</p>
					</div>
				</div>
			</div>
		{:else}
			<div class="bg-gradient-to-r from-blue-600 to-purple-600 py-16">
				<div class="container mx-auto px-4 text-center text-white">
					<h1 class="mb-4 text-3xl font-bold md:text-5xl">{data.event?.title}</h1>
					<p class="text-lg opacity-90 md:text-xl">{data.event?.short_description}</p>
				</div>
			</div>
		{/if}
	</div>

	<div class="container mx-auto max-w-6xl px-4 py-8">
		<div class="grid gap-8 lg:grid-cols-3">
			<!-- Event Details -->
			<div class="space-y-6 lg:col-span-2">
				<!-- Event Info Cards -->
				<div class="grid gap-4 md:grid-cols-2">
					<!-- Date & Time -->
					<div class="rounded-lg border bg-white p-6 shadow-sm">
						<div class="mb-3 flex items-center gap-3">
							<Calendar class="h-5 w-5 text-blue-600" />
							<h3 class="font-semibold">Date & Time</h3>
						</div>
						<div class="space-y-2 text-sm text-gray-600">
							<p>
								<strong>Start:</strong>
								{formatDate(data.event?.start_date)} at {formatTime(data.event?.start_time)}
							</p>
							<p>
								<strong>End:</strong>
								{formatDate(data.event?.end_date)} at {formatTime(data.event?.end_time)}
							</p>
						</div>
					</div>

					<!-- Location -->
					<div class="rounded-lg border bg-white p-6 shadow-sm">
						<div class="mb-3 flex items-center gap-3">
							<MapPin class="h-5 w-5 text-green-600" />
							<h3 class="font-semibold">Location</h3>
						</div>
						<div class="space-y-2 text-sm text-gray-600">
							<p>{data.event?.location}</p>
							{#if data.event?.location_url}
								<a
									href={data.event.location_url}
									target="_blank"
									class="flex items-center gap-1 text-blue-600 hover:underline"
								>
									<Globe class="h-4 w-4" />
									View on Map
								</a>
							{/if}
						</div>
					</div>

					<!-- Registration Status -->
					{#if maxRegistrations}
						<div class="rounded-lg border bg-white p-6 shadow-sm">
							<div class="mb-3 flex items-center gap-3">
								<Users class="h-5 w-5 text-purple-600" />
								<h3 class="font-semibold">Registration</h3>
							</div>
							<div class="space-y-2 text-sm text-gray-600">
								<p><strong>Capacity:</strong> {maxRegistrations} attendees</p>
								<p><strong>Registered:</strong> {registrationCount}</p>
								<div class="h-2 w-full rounded-full bg-gray-200">
									<div
										class="h-2 rounded-full bg-blue-600 transition-all duration-300"
										style="width: {Math.min((registrationCount / maxRegistrations) * 100, 100)}%"
									></div>
								</div>
							</div>
						</div>
					{/if}
				</div>

				<!-- Event Description -->
				{#if data.event?.long_description}
					<div class="rounded-lg border bg-white p-6 shadow-sm">
						<h3 class="mb-4 font-semibold">About This Event</h3>
						<div class="prose prose-sm max-w-none">
							<Markdown content={data.event.long_description} />
						</div>
					</div>
				{/if}

				<!-- Arabic Description -->
				{#if data.event?.long_description_arabic}
					<div class="rounded-lg border bg-white p-6 shadow-sm" dir="rtl">
						<h3 class="mb-4 font-semibold">حول هذا الحدث</h3>
						<div class="prose prose-sm max-w-none">
							<Markdown content={data.event.long_description_arabic} />
						</div>
					</div>
				{/if}
			</div>

			<!-- Registration Form -->
			<div class="lg:col-span-1">
				{#if isPreview}
					<Alert class="mb-6">
						<AlertDescription>
							<strong>Preview Mode:</strong> This is how your registration form will appear to users.
						</AlertDescription>
					</Alert>
				{/if}

				{#if isEventFull}
					<Alert class="mb-6" variant="destructive">
						<AlertDescription>
							<strong>Event Full:</strong> Registration is currently full. You may join the waiting list.
						</AlertDescription>
					</Alert>
				{/if}

				<!-- Dynamic Registration Form -->
				{#if eventId}
					<DynamicRegistrationForm
						{eventId}
						eventSlug={$page.params.slug}
						{formFields}
						preview={isPreview}
					/>
				{:else}
					<div class="rounded-lg border bg-white p-6 shadow-sm">
						<div class="text-center text-gray-500">
							<p>Registration form is not available.</p>
						</div>
					</div>
				{/if}
			</div>
		</div>
	</div>

	<Footer />
</main>

<style>
	.prose h1,
	.prose h2,
	.prose h3,
	.prose h4,
	.prose h5,
	.prose h6 {
		margin-top: 1.5em;
		margin-bottom: 0.5em;
	}

	.prose p {
		margin-bottom: 1em;
	}

	.prose ul,
	.prose ol {
		margin-top: 1em;
		margin-bottom: 1em;
		padding-left: 1.5em;
	}

	.prose li {
		margin-bottom: 0.25em;
	}
</style>

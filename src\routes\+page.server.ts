import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals: { supabase } }) => {
    const { data: events, error: eventsError } = await supabase
        .from('events')
        .select('id, title, title_slug, short_description,  image_url, location, start_date, location_url')
        .eq('is_published', 'true')
        .eq('is_private', 'false')
        .gt('start_date', new Date().toISOString())
        // .select()
        .order('start_date', { ascending: true });

    if (eventsError) {
        console.error('Error in Events Data Query: ', eventsError);
        // return { error };
    }
    // console.log('Events: ', events);
    return { events: events ?? [] };

};

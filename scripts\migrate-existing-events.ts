// Migration script to initialize standard fields for existing events
// Run this after deploying the flexible forms feature

import { supabaseAdmin } from '../src/lib/server/db/supabaseAdmin';
import { initializeStandardFields } from '../src/lib/server/formFields';

async function migrateExistingEvents() {
  console.log('Starting migration of existing events...');

  try {
    // Get all existing events
    const { data: events, error: eventsError } = await supabaseAdmin
      .from('events')
      .select('id, title');

    if (eventsError) {
      console.error('Error fetching events:', eventsError);
      return;
    }

    if (!events || events.length === 0) {
      console.log('No events found to migrate.');
      return;
    }

    console.log(`Found ${events.length} events to migrate.`);

    // Initialize standard fields for each event
    for (const event of events) {
      console.log(`Migrating event: ${event.title} (${event.id})`);
      
      const result = await initializeStandardFields(event.id);
      
      if (result.success) {
        console.log(`✓ Successfully initialized fields for ${event.title}`);
      } else {
        console.error(`✗ Failed to initialize fields for ${event.title}:`, result.error);
      }
    }

    console.log('Migration completed!');
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

// Run the migration
migrateExistingEvents();

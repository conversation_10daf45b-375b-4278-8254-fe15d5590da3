# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added

- Email template customization for registration and reminder emails
- QR code scanning functionality for event check-in/check-out
- CSV import/export for guest management
- Event analytics dashboard
- Arabic language support for event descriptions
- QR code scanner integration in input fields for easy code scanning
- Event title display in admin dashboard header for better context
- New QR scanner component implementation with improved UX

### Changed

- Updated to SvelteKit 5.0
- Improved form validation using Superforms
- Enhanced UI components with Bits UI
- Hidden social sharing buttons temporarily
- Improved event context visibility in admin interface

### Fixed

- Email template validation and error handling
- Guest list pagination issues
- QR code scanner compatibility issues
- UI/UX improvements in event management dashboard

## [0.0.1] - 2024-XX-XX

### Added

- Initial release
- Basic event management functionality
- User authentication with Supabase
- Event registration system
- Email notification system
- Basic analytics

<script lang="ts">
	import { goto } from '$app/navigation';
	import { Button } from '$lib/components/ui/button';
	import * as Table from '$lib/components/ui/table/index.js';
	import type { PageData } from './$types';
	import { onMount } from 'svelte';
	import { analytics } from '$lib/analytics/analytics';

	import DataTable from './data-table.svelte';
	import { columns, type Guest } from './columns.js';

	export let data;
	// To change the "dataa" variable, you need to change the "data" variable in the parent component. ie in data-table.svelte
	let dataa = data.guestsData;

	$: eventId = data.params.eventid;

	// Track guest list view
	onMount(() => {
		if (eventId && data.count !== undefined) {
			analytics.trackGuestListViewed(eventId, data.count);
		}
	});

	// Track button clicks
	function trackGuestAction(action: string, buttonName: string) {
		analytics.trackButtonClick(buttonName, 'guest_list', {
			event_id: eventId,
			action,
			guest_count: data.count
		});
	}
</script>

<header class="m-4 flex flex-row items-center justify-between">
	<h1 class=" text-4xl font-bold">All Guests ({data.count})</h1>

	<div class=" space-y-3">
		<Button href="#" onclick={() => trackGuestAction('add_guest', 'add_guest_button')}>
			Add Guest
		</Button>
		<Button
			href="/private/events/{data.params.eventid}/guestlist/import"
			onclick={() => trackGuestAction('import_guests', 'import_guests_button')}
		>
			Import Guests
		</Button>
		<Button
			href="/private/events/{data.params.eventid}/guestlist/qr-scanner"
			onclick={() => trackGuestAction('qr_scanner', 'qr_scanner_button')}
		>
			Scan QR Code
		</Button>
		<Button
			href="/private/events/{data.params.eventid}/guestlist/api/export-csv"
			download="exported_data.csv"
			onclick={() => {
				trackGuestAction('export_csv', 'export_csv_button');
				analytics.trackGuestDataExported(eventId, 'csv');
			}}
		>
			Export
		</Button>
		<Button
			href="/private/events/{data.params.eventid}/guestlist/cert"
			onclick={() => trackGuestAction('certificates', 'certificates_button')}
		>
			Certificates
		</Button>
		<Button
			href="/private/events/{data.params.eventid}/guestlist/waiting-list"
			onclick={() => trackGuestAction('waiting_list', 'waiting_list_button')}
		>
			View Waiting List
		</Button>
	</div>
</header>

<main>
	<div class="container mx-auto py-10">
		<DataTable {dataa} {columns} />
	</div>
</main>

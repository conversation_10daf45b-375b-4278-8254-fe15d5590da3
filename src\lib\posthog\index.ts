// PostHog integration for SvelteKit
// Main entry point for all PostHog functionality

// Configuration and initialization
export {
	initializePostHog,
	enableSessionReplay,
	disableSessionReplay,
	optOut,
	optIn,
	identifyUser as configIdentifyUser,
	resetUser as configResetUser,
	posthog
} from './config';

// Stores and reactive state
export {
	posthogLoaded,
	posthogError,
	currentUser,
	sessionReplayEnabled,
	featureFlags,
	analyticsConsent,
	posthogAvailable,
	eventQueue,
	updateUser,
	clearUser,
	updateFeatureFlags,
	grantAnalyticsConsent,
	revokeAnalyticsConsent,
	toggleSessionReplay,
	queueEvent,
	processEventQueue
} from './stores';

// Event tracking utilities
export {
	trackEvent,
	trackPageView,
	trackButtonClick,
	trackFormSubmit,
	trackError,
	trackFeatureUsage,
	trackUserAction,
	identifyUser,
	setUserProperties,
	logoutUser,
	getFeatureFlag,
	isFeatureEnabled,
	getAllFeatureFlags,
	startSessionReplay,
	stopSessionReplay,
	optOutOfTracking,
	optInToTracking,
	hasOptedOut,
	identifyGroup,
	getVariant,
	debugPostHog
} from './utils';

// Error tracking
export {
	setupErrorTracking,
	trackError as trackErrorContext,
	trackJavaScriptError,
	trackNetworkError,
	trackApiError,
	trackUserActionError,
	trackComponentError,
	withErrorTracking,
	withSyncErrorTracking,
	trackPerformanceIssue,
	trackMemoryUsage
} from './error-tracking';

// Testing utilities
export {
	testPostHogInitialization,
	testEventTracking,
	testUserIdentification,
	testFeatureFlags,
	testSessionReplay,
	testPrivacyControls,
	testConfiguration,
	runPostHogTestSuite,
	formatTestResults,
	runPostHogTests
} from './test-utils';

// Types
export type {
	PostHogEventProperties,
	PostHogUserProperties,
	PostHogErrorContext,
	PostHogFeatureFlag,
	PostHogFeatureFlags,
	PostHogInitOptions,
	PostHogUser,
	PostHogAnalyticsConsent,
	PostHogEventQueueItem,
	PostHogEventName,
	PageViewProperties,
	ButtonClickProperties,
	FormSubmitProperties,
	ErrorProperties,
	FeatureUsageProperties,
	UserActionProperties,
	EcommerceProperties,
	PostHogTestResult,
	PostHogTestSuite,
	ErrorType,
	ErrorContext,
	PrivacySettings,
	FeatureFlagConfig,
	AnalyticsDashboardData,
	PerformanceMetrics,
	ABTestVariant,
	ABTestConfig,
	PostHogEventHandler,
	PostHogUserIdentifier,
	PostHogCallback,
	PostHogAsyncCallback,
	ErrorBoundaryProps,
	AnalyticsProviderProps,
	UsePostHogReturn,
	PostHogConfigValidation,
	RequiredPostHogConfig,
	OptionalPostHogConfig,
	TrackEventFunction,
	TrackPageViewFunction,
	TrackErrorFunction
} from './types';

// Components
export { default as ErrorBoundary } from '../components/ErrorBoundary.svelte';
export { default as PostHogExamples } from '../components/PostHogExamples.svelte';

// Default export for convenience
export { posthog as default } from './config';

import { browser } from '$app/environment';
import { PUBLIC_POSTHOG_KEY, PUBLIC_POSTHOG_HOST } from '$env/static/public';
import posthog from 'posthog-js';

// PostHog instance
let posthogInstance: typeof posthog | null = null;

// Configuration
const POSTHOG_CONFIG = {
	api_host: PUBLIC_POSTHOG_HOST || 'https://us.i.posthog.com',
	person_profiles: 'identified_only',
	loaded: (posthog: any) => {
		if (import.meta.env.DEV) {
			console.log('PostHog loaded successfully');
		}
	},
	capture_pageview: false, // We'll handle page views manually
	capture_pageleave: true,
	autocapture: true, // Enable automatic event capture
	session_recording: {
		enabled: true,
		maskAllInputs: true, // Mask sensitive inputs for privacy
		maskInputOptions: {
			password: true,
			email: false // We can track email field interactions
		}
	},
	// Error tracking
	capture_heatmaps: true,
	// Privacy settings
	respect_dnt: true,
	// Debug mode in development
	debug: import.meta.env.DEV
};

// Initialize PostHog
export function initPostHog(): void {
	if (browser && PUBLIC_POSTHOG_KEY && !posthogInstance) {
		posthog.init(PUBLIC_POSTHOG_KEY, POSTHOG_CONFIG);
		posthogInstance = posthog;
		
		// Set up global error tracking
		setupErrorTracking();
		
		if (import.meta.env.DEV) {
			console.log('PostHog initialized with key:', PUBLIC_POSTHOG_KEY.substring(0, 10) + '...');
		}
	}
}

// Setup error tracking
function setupErrorTracking(): void {
	if (!browser) return;

	// Track JavaScript errors
	window.addEventListener('error', (event) => {
		trackEvent('javascript_error', {
			error_message: event.message,
			error_filename: event.filename,
			error_lineno: event.lineno,
			error_colno: event.colno,
			error_stack: event.error?.stack
		});
	});

	// Track unhandled promise rejections
	window.addEventListener('unhandledrejection', (event) => {
		trackEvent('unhandled_promise_rejection', {
			error_message: event.reason?.message || 'Unhandled promise rejection',
			error_stack: event.reason?.stack
		});
	});
}

// Check if PostHog is available
export function isPostHogAvailable(): boolean {
	return browser && posthogInstance && posthogInstance.__loaded;
}

// Event tracking functions
export function trackEvent(eventName: string, properties?: Record<string, any>): void {
	if (isPostHogAvailable()) {
		posthogInstance!.capture(eventName, properties);
		if (import.meta.env.DEV) {
			console.log(`📊 Event tracked: ${eventName}`, properties);
		}
	} else if (import.meta.env.DEV) {
		console.log(`📊 Event queued (PostHog not ready): ${eventName}`, properties);
	}
}

export function trackPageView(pageName?: string, properties?: Record<string, any>): void {
	if (!browser) return;
	
	trackEvent('$pageview', {
		$current_url: window.location.href,
		page_name: pageName || window.location.pathname,
		referrer: document.referrer,
		...properties
	});
}

export function trackButtonClick(buttonName: string, location?: string, properties?: Record<string, any>): void {
	trackEvent('button_click', {
		button_name: buttonName,
		location: location || window.location.pathname,
		...properties
	});
}

export function trackFormSubmit(formName: string, properties?: Record<string, any>): void {
	trackEvent('form_submit', {
		form_name: formName,
		page: window.location.pathname,
		...properties
	});
}

export function trackLinkClick(linkName: string, linkUrl: string, location?: string): void {
	trackEvent('link_click', {
		link_name: linkName,
		link_url: linkUrl,
		location: location || 'unknown'
	});
}

export function trackError(error: Error, context?: Record<string, any>): void {
	trackEvent('error_occurred', {
		error_message: error.message,
		error_stack: error.stack,
		error_name: error.name,
		page: window.location.pathname,
		...context
	});
}

export function trackPerformance(metric: string, value: number, additionalData?: Record<string, any>): void {
	trackEvent('performance_metric', {
		metric,
		value,
		page: window.location.pathname,
		...additionalData
	});
}

// User identification
export function identifyUser(userId: string, properties?: Record<string, any>): void {
	if (isPostHogAvailable()) {
		posthogInstance!.identify(userId, properties);
		if (import.meta.env.DEV) {
			console.log(`👤 User identified: ${userId}`, properties);
		}
	}
}

export function setUserProperties(properties: Record<string, any>): void {
	if (isPostHogAvailable()) {
		posthogInstance!.setPersonProperties(properties);
	}
}

export function resetUser(): void {
	if (isPostHogAvailable()) {
		posthogInstance!.reset();
	}
}

// Feature flags
export function getFeatureFlag(flagName: string): boolean | string | undefined {
	if (isPostHogAvailable()) {
		return posthogInstance!.getFeatureFlag(flagName);
	}
	return undefined;
}

export function isFeatureEnabled(flagName: string): boolean {
	const flag = getFeatureFlag(flagName);
	return Boolean(flag);
}

// Session replay
export function startSessionReplay(): void {
	if (isPostHogAvailable()) {
		posthogInstance!.startSessionRecording();
	}
}

export function stopSessionReplay(): void {
	if (isPostHogAvailable()) {
		posthogInstance!.stopSessionRecording();
	}
}

// Privacy controls
export function optOut(): void {
	if (isPostHogAvailable()) {
		posthogInstance!.opt_out_capturing();
	}
}

export function optIn(): void {
	if (isPostHogAvailable()) {
		posthogInstance!.opt_in_capturing();
	}
}

export function hasOptedOut(): boolean {
	if (isPostHogAvailable()) {
		return posthogInstance!.has_opted_out_capturing();
	}
	return false;
}

// Export the PostHog instance for advanced usage
export { posthogInstance as posthog };

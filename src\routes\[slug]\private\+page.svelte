<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import * as Card from '$lib/components/ui/card';
	import { enhance } from '$app/forms';
	import type { PageData } from './$types';

	export let data: PageData;
</script>

<svelte:head>
	<title>Private Event Access - {data.event.title}</title>
</svelte:head>

<div class="container mx-auto max-w-md py-16">
	<Card.Root>
		<Card.Header class="text-center">
			<Card.Title class="text-2xl">Private Event</Card.Title>
			<Card.Description>
				This is a private event. Please enter the access code to continue.
			</Card.Description>
		</Card.Header>
		<Card.Content>
			<div class="mb-6 text-center">
				<h2 class="text-xl font-semibold">{data.event.title}</h2>
				<p class="text-muted-foreground">{data.event.short_description}</p>
			</div>

			<form method="POST" action="?/verifyAccess" use:enhance>
				<div class="space-y-4">
					<div class="space-y-2">
						<Label for="access_code">Access Code</Label>
						<Input
							id="access_code"
							name="access_code"
							type="text"
							placeholder="Enter access code"
							required
						/>
					</div>
					<Button type="submit" class="w-full">Access Event</Button>
				</div>
			</form>

			{#if data.error}
				<div class="mt-4 rounded-md bg-red-50 p-4">
					<p class="text-sm text-red-800">{data.error}</p>
				</div>
			{/if}
		</Card.Content>
	</Card.Root>
</div>

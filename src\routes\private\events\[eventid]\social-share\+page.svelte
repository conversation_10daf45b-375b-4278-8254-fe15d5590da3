<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Card } from '$lib/components/ui/card';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Upload } from 'lucide-svelte';
	import type { PageData } from './$types';

	export let data: PageData;

	let shareText = data.shareText || '';
	let imageFile: File | null = null;
	let previewUrl: string = data.shareImageUrl || '';
	let saving = false;
	let exists = data.exists;

	function handleImageUpload(event: Event) {
		const input = event.target as HTMLInputElement;
		if (input.files && input.files[0]) {
			imageFile = input.files[0];
			// Create preview URL
			previewUrl = URL.createObjectURL(input.files[0]);
		}
	}

	function triggerFileInput() {
		document.getElementById('image-upload')?.click();
	}

	async function handleSubmit() {
		saving = true;
		const formData = new FormData();
		formData.append('shareText', shareText);
		if (imageFile) {
			formData.append('shareImage', imageFile);
		}

		try {
			const response = await fetch('?/updateSocialShare', {
				method: 'POST',
				body: formData
			});

			if (!response.ok) throw new Error('Failed to save');

			exists = true; // Set exists to true after successful save

			// Reset file input but keep the preview
			const fileInput = document.getElementById('image-upload') as HTMLInputElement;
			if (fileInput) fileInput.value = '';
		} catch (error) {
			console.error('Error saving:', error);
		} finally {
			saving = false;
		}
	}
</script>

<div class="container mx-auto p-8">
	<h1 class="mb-6 text-3xl font-bold">Social Share Settings</h1>

	<div class="mx-auto max-w-2xl space-y-6">
		<Card class="p-6">
			<form on:submit|preventDefault={handleSubmit} class="space-y-6">
				<div class="space-y-4">
					<h2 class="text-xl font-semibold">Share Image</h2>
					<p class="text-sm text-muted-foreground">
						{exists ? 'Update' : 'Add'} the image that will be used when attendees share the event on
						social media.
					</p>
					<div class="flex items-center gap-4">
						<Input
							type="file"
							accept="image/*"
							class="hidden"
							id="image-upload"
							onchange={handleImageUpload}
						/>
						<Button variant="outline" type="button" onclick={triggerFileInput}>
							<Upload class="mr-2 h-4 w-4" />
							{previewUrl ? 'Change Image' : 'Upload Image'}
						</Button>
						{#if imageFile}
							<span class="text-sm text-green-600">{imageFile.name}</span>
						{/if}
					</div>

					{#if previewUrl}
						<div class="mt-4 rounded-lg border p-2">
							<img
								src={previewUrl}
								alt="Share preview"
								class="max-h-[300px] w-full rounded object-cover"
							/>
						</div>
					{/if}
				</div>

				<div class="space-y-4">
					<h2 class="text-xl font-semibold">Share Text</h2>
					<Textarea
						bind:value={shareText}
						placeholder="Enter the text that will be shared on social media..."
						rows={4}
					/>
					<p class="text-sm text-muted-foreground">
						This text will be used when attendees share the event on social media.
					</p>
				</div>

				<Button type="submit" disabled={saving}>
					{saving ? 'Saving...' : exists ? 'Update Settings' : 'Save Settings'}
				</Button>
			</form>
		</Card>
	</div>
</div>

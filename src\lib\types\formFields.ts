// TypeScript types for flexible form fields system

export type FormFieldType = 
  | 'text' 
  | 'email' 
  | 'phone' 
  | 'select' 
  | 'checkbox' 
  | 'textarea' 
  | 'number' 
  | 'date' 
  | 'url';

export interface ValidationRules {
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  min?: number;
  max?: number;
  required?: boolean;
  customMessage?: string;
}

export interface FieldOptions {
  options?: string[];
  multiple?: boolean;
  allowOther?: boolean;
}

export interface FormField {
  id: string;
  eventId: string;
  fieldKey: string;
  fieldLabel: string;
  fieldType: FormFieldType;
  fieldOptions?: FieldOptions;
  isRequired: boolean;
  isStandardField: boolean;
  isEnabled: boolean;
  displayOrder: number;
  validationRules?: ValidationRules;
  placeholderText?: string;
  helpText?: string;
  createdAt: string;
  updatedAt: string;
}

export interface FormResponse {
  id: string;
  eventId: string;
  guestId: number;
  fieldKey: string;
  fieldValue: string;
  createdAt: string;
  updatedAt: string;
}

export interface GuestFormResponse {
  fieldKey: string;
  fieldLabel: string;
  fieldType: FormFieldType;
  fieldValue: string;
}

export interface FormFieldCreate {
  eventId: string;
  fieldKey: string;
  fieldLabel: string;
  fieldType: FormFieldType;
  fieldOptions?: FieldOptions;
  isRequired?: boolean;
  isStandardField?: boolean;
  isEnabled?: boolean;
  displayOrder?: number;
  validationRules?: ValidationRules;
  placeholderText?: string;
  helpText?: string;
}

export interface FormFieldUpdate {
  fieldLabel?: string;
  fieldType?: FormFieldType;
  fieldOptions?: FieldOptions;
  isRequired?: boolean;
  isEnabled?: boolean;
  displayOrder?: number;
  validationRules?: ValidationRules;
  placeholderText?: string;
  helpText?: string;
}

export interface StandardField {
  fieldKey: string;
  fieldLabel: string;
  fieldType: FormFieldType;
  isRequired: boolean;
  defaultEnabled: boolean;
  displayOrder: number;
  description?: string;
}

export interface DynamicFormData {
  [fieldKey: string]: string | string[] | boolean;
}

export interface FormSubmissionData {
  eventId: string;
  guestId?: number;
  responses: DynamicFormData;
}

export interface FormValidationError {
  fieldKey: string;
  message: string;
}

export interface FormValidationResult {
  isValid: boolean;
  errors: FormValidationError[];
}

// Standard fields configuration
export const STANDARD_FIELDS: StandardField[] = [
  {
    fieldKey: 'first_name',
    fieldLabel: 'First Name',
    fieldType: 'text',
    isRequired: true,
    defaultEnabled: true,
    displayOrder: 1,
    description: 'Participant\'s first name'
  },
  {
    fieldKey: 'middle_name',
    fieldLabel: 'Middle Name',
    fieldType: 'text',
    isRequired: false,
    defaultEnabled: false,
    displayOrder: 2,
    description: 'Participant\'s middle name (optional)'
  },
  {
    fieldKey: 'last_name',
    fieldLabel: 'Last Name',
    fieldType: 'text',
    isRequired: true,
    defaultEnabled: true,
    displayOrder: 3,
    description: 'Participant\'s last name'
  },
  {
    fieldKey: 'full_name',
    fieldLabel: 'Full Name',
    fieldType: 'text',
    isRequired: false,
    defaultEnabled: false,
    displayOrder: 4,
    description: 'Complete name (alternative to first/last name)'
  },
  {
    fieldKey: 'email',
    fieldLabel: 'Email Address',
    fieldType: 'email',
    isRequired: true,
    defaultEnabled: true,
    displayOrder: 5,
    description: 'Primary email address for communication'
  },
  {
    fieldKey: 'mobile',
    fieldLabel: 'Mobile Number',
    fieldType: 'phone',
    isRequired: false,
    defaultEnabled: true,
    displayOrder: 6,
    description: 'Primary mobile/phone number'
  },
  {
    fieldKey: 'whatsapp',
    fieldLabel: 'WhatsApp Number',
    fieldType: 'phone',
    isRequired: false,
    defaultEnabled: false,
    displayOrder: 7,
    description: 'WhatsApp contact number'
  },
  {
    fieldKey: 'organization',
    fieldLabel: 'Company/Organization',
    fieldType: 'text',
    isRequired: false,
    defaultEnabled: true,
    displayOrder: 8,
    description: 'Company or organization name'
  },
  {
    fieldKey: 'designation',
    fieldLabel: 'Designation/Role',
    fieldType: 'text',
    isRequired: false,
    defaultEnabled: false,
    displayOrder: 9,
    description: 'Job title or role within organization'
  }
];

// Field type configurations for UI
export interface FieldTypeConfig {
  label: string;
  description: string;
  hasOptions: boolean;
  defaultValidation?: ValidationRules;
  icon?: string;
}

export const FIELD_TYPE_CONFIGS: Record<FormFieldType, FieldTypeConfig> = {
  text: {
    label: 'Text Input',
    description: 'Single line text input',
    hasOptions: false,
    defaultValidation: { maxLength: 255 }
  },
  email: {
    label: 'Email',
    description: 'Email address input with validation',
    hasOptions: false,
    defaultValidation: { pattern: '^[^@]+@[^@]+\\.[^@]+$' }
  },
  phone: {
    label: 'Phone Number',
    description: 'Phone number input',
    hasOptions: false,
    defaultValidation: { pattern: '^\\+?[0-9]{8,14}$' }
  },
  select: {
    label: 'Dropdown',
    description: 'Dropdown selection with predefined options',
    hasOptions: true
  },
  checkbox: {
    label: 'Checkbox',
    description: 'Multiple choice checkboxes',
    hasOptions: true
  },
  textarea: {
    label: 'Text Area',
    description: 'Multi-line text input',
    hasOptions: false,
    defaultValidation: { maxLength: 1000 }
  },
  number: {
    label: 'Number',
    description: 'Numeric input',
    hasOptions: false
  },
  date: {
    label: 'Date',
    description: 'Date picker input',
    hasOptions: false
  },
  url: {
    label: 'URL',
    description: 'Website URL input',
    hasOptions: false,
    defaultValidation: { pattern: '^https?://.+' }
  }
};

// Database table types (to be added to database.types.ts)
export interface DatabaseFormField {
  Row: {
    id: string;
    event_id: string;
    field_key: string;
    field_label: string;
    field_type: FormFieldType;
    field_options: FieldOptions | null;
    is_required: boolean;
    is_standard_field: boolean;
    is_enabled: boolean;
    display_order: number;
    validation_rules: ValidationRules | null;
    placeholder_text: string | null;
    help_text: string | null;
    created_at: string;
    updated_at: string;
  };
  Insert: {
    id?: string;
    event_id: string;
    field_key: string;
    field_label: string;
    field_type: FormFieldType;
    field_options?: FieldOptions | null;
    is_required?: boolean;
    is_standard_field?: boolean;
    is_enabled?: boolean;
    display_order?: number;
    validation_rules?: ValidationRules | null;
    placeholder_text?: string | null;
    help_text?: string | null;
  };
  Update: {
    field_label?: string;
    field_type?: FormFieldType;
    field_options?: FieldOptions | null;
    is_required?: boolean;
    is_enabled?: boolean;
    display_order?: number;
    validation_rules?: ValidationRules | null;
    placeholder_text?: string | null;
    help_text?: string | null;
    updated_at?: string;
  };
}

export interface DatabaseFormResponse {
  Row: {
    id: string;
    event_id: string;
    guest_id: number;
    field_key: string;
    field_value: string | null;
    created_at: string;
    updated_at: string;
  };
  Insert: {
    id?: string;
    event_id: string;
    guest_id: number;
    field_key: string;
    field_value?: string | null;
  };
  Update: {
    field_value?: string | null;
    updated_at?: string;
  };
}

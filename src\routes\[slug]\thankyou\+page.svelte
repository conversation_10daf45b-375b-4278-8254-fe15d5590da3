<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Card } from '$lib/components/ui/card';
	import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '$lib/components/ui/tabs';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Input } from '$lib/components/ui/input';
	import {
		Facebook,
		Instagram,
		Linkedin,
		Twitter,
		Share2,
		Copy,
		Check,
		Calendar,
		Upload,
		Mail,
		ArrowLeft
	} from 'lucide-svelte';
	import html2canvas from 'html2canvas';
	import { formatDate as formatDisplayDate, formatTime } from '$lib/utils/formatdatetime.js';
	import googlecalendar from '$lib/images/googlecalendar.png';
	import applecalendar from '$lib/images/applecalendar.png';
	import outlookcalendar from '$lib/images/outlook-calendar-480.png';
	import office365calendar from '$lib/images/office-365-480.png';

	export let data;

	let userImage: string | null = null;
	let userName = '';
	let copied = false;
	let bannerRef: HTMLDivElement;

	// Get event data from the server
	const { eventData, slug, socialShare } = data;

	const shareText = socialShare.share_text
		? socialShare.share_text
		: `I'm excited to attend ${eventData.name} on ${formatDisplayDate(eventData.date)}! Join me at ${eventData.location} for an amazing experience. #${eventData.name.replace(/\s+/g, '')}`;

	// Format date for calendar URLs
	function formatCalendarDate(date: string, time: string) {
		return date.replace(/-/g, '') + 'T' + time.replace(/:/g, '') + '00';
	}

	const startDateTime = formatCalendarDate(eventData.date, eventData.startTime);
	const endDateTime = formatCalendarDate(eventData.date, eventData.endTime);

	// Generate calendar URLs
	function getGoogleCalendarUrl() {
		return `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(eventData.name)}&dates=${startDateTime}/${endDateTime}&details=${encodeURIComponent(eventData.description)}&location=${encodeURIComponent(eventData.location)}`;
	}

	function getOutlookCalendarUrl() {
		return `https://outlook.live.com/calendar/0/deeplink/compose?subject=${encodeURIComponent(eventData.name)}&startdt=${eventData.date}T${eventData.startTime}&enddt=${eventData.date}T${eventData.endTime}&body=${encodeURIComponent(eventData.description)}&location=${encodeURIComponent(eventData.location)}`;
	}

	function getAppleCalendarUrl() {
		return `data:text/calendar;charset=utf-8,BEGIN:VCALENDAR%0AVERSION:2.0%0ABEGIN:VEVENT%0ADTSTART:${startDateTime}%0ADTEND:${endDateTime}%0ASUMMARY:${encodeURIComponent(eventData.name)}%0ADESCRIPTION:${encodeURIComponent(eventData.description)}%0ALOCATION:${encodeURIComponent(eventData.location)}%0AEND:VEVENT%0AEND:VCALENDAR`;
	}

	function getOffice365CalendarUrl() {
		return `https://outlook.office.com/calendar/0/deeplink/compose?subject=${encodeURIComponent(eventData.name)}&startdt=${eventData.date}T${eventData.startTime}&enddt=${eventData.date}T${eventData.endTime}&body=${encodeURIComponent(eventData.description)}&location=${encodeURIComponent(eventData.location)}`;
	}

	async function uploadGeneratedImage() {
		const imageData = await generateShareableImage();
		if (!imageData) return null;

		// Convert base64 to blob
		const base64Response = await fetch(imageData);
		const blob = await base64Response.blob();

		// Create form data
		const formData = new FormData();
		formData.append('shareImage', blob, `${eventData.name.replace(/\s+/g, '-')}-share.png`);

		try {
			// Upload to server
			const response = await fetch(`/private/api/upload-share-image`, {
				method: 'POST',
				body: formData
			});

			if (!response.ok) throw new Error('Failed to upload image');

			const { imageUrl } = await response.json();
			return imageUrl;
		} catch (error) {
			console.error('Error uploading image:', error);
			return null;
		}
	}

	async function shareToSocial(platform: string) {
		if (platform === 'download') {
			const imageUrl = await generateShareableImage();
			if (!imageUrl) {
				alert('Could not generate image to share');
				return;
			}
			const link = document.createElement('a');
			link.href = imageUrl;
			link.download = `${eventData.name.replace(/\s+/g, '-')}-share.png`;
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
			return;
		}

		const imageUrl = await uploadGeneratedImage();
		if (!imageUrl) {
			alert('Could not generate image to share');
			return;
		}

		// const shareUrl = encodeURIComponent(window.location.href);
		// const shareTextEncoded = encodeURIComponent(shareText);
		// const imageUrlEncoded = encodeURIComponent(imageUrl);
		// const hashtags = eventData.name.replace(/\s+/g, ''); // Remove spaces for hashtag
		const shareUrl = window.location.href;
		const shareTextEncoded = shareText;
		const imageUrlEncoded = imageUrl;
		const hashtags = eventData.name.replace(/\s+/g, ''); // Remove spaces for hashtag

		console.log('Share URL:', shareUrl);
		console.log('Share Text:', shareTextEncoded);
		console.log('Image URL:', imageUrlEncoded);
		console.log('Hashtags:', hashtags);

		switch (platform) {
			case 'facebook':
				// Facebook sharing
				window.open(
					// 	`https://www.facebook.com/sharer/sharer.php?u=` +
					// 		`display=popup` +
					// 		`&href=${shareUrl}` +
					// 		`&quote=${shareTextEncoded}` +
					// 		`&hashtag=${encodeURIComponent('#' + hashtags)}` +
					// 		`&picture=${imageUrlEncoded}`,
					// 	'facebook-share-dialog',
					// 	'width=800,height=600'
					// );
					`https://www.facebook.com/sharer/sharer.php?u=` +
						`&href=${shareUrl}` +
						`&quote=${shareTextEncoded}` +
						`&hashtag=${encodeURIComponent('#' + hashtags)}` +
						`&picture=${imageUrlEncoded}`,
					'facebook-share-dialog',
					'width=800,height=600'
				);
				break;

			case 'twitter':
				// Twitter/X sharing
				window.open(
					`https://twitter.com/intent/tweet?` +
						`text=${shareTextEncoded}` +
						`&url=${shareUrl}` +
						`&hashtags=${hashtags}`,
					'twitter-share-dialog',
					'width=800,height=600'
				);
				break;

			case 'linkedin':
				// LinkedIn sharing
				window.open(
					`https://www.linkedin.com/sharing/share-offsite/?` +
						`url=${shareUrl}` +
						`&title=${encodeURIComponent(eventData.name)}` +
						`&summary=${shareTextEncoded}` +
						`&source=${imageUrlEncoded}`,
					'linkedin-share-dialog',
					'width=800,height=600'
				);
				break;

			case 'whatsapp':
				// WhatsApp sharing
				const whatsappText = `${shareText}\n\n${window.location.href}`;
				window.open(`https://wa.me/?text=${encodeURIComponent(whatsappText)}`, '_blank');
				break;

			// case 'telegram':
			// 	// Telegram sharing
			// 	window.open(
			// 		`https://t.me/share/url?` + `url=${shareUrl}` + `&text=${shareTextEncoded}`,
			// 		'_blank'
			// 	);
			// 	break;

			case 'email':
				// Email sharing
				const emailSubject = `Join me at ${eventData.name}`;
				const emailBody = `${shareText}\n\nRegister here: ${window.location.href}`;
				window.location.href = `mailto:?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`;
				break;

			case 'instagram':
				// Instagram doesn't support direct sharing via URL
				alert(
					'To share on Instagram:\n1. Save the image\n2. Open Instagram\n3. Create a new post\n4. Copy and paste this text:\n\n' +
						shareText
				);
				// Trigger image download for Instagram
				const link = document.createElement('a');
				link.href = imageUrl;
				link.download = `${eventData.name.replace(/\s+/g, '-')}-share.png`;
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);
				break;
		}
	}

	function copyToClipboard() {
		navigator.clipboard.writeText(shareText);
		copied = true;
		setTimeout(() => (copied = false), 2000);
	}

	function handleImageUpload(e: Event) {
		const input = e.target as HTMLInputElement;
		if (input.files && input.files[0]) {
			const file = input.files[0];
			const reader = new FileReader();

			reader.onload = (event) => {
				if (event.target) {
					userImage = event.target.result as string;
				}
			};

			reader.readAsDataURL(file);
		}
	}

	async function generateShareableImage() {
		if (bannerRef) {
			try {
				// Wait for background image to load
				const backgroundImg = bannerRef.querySelector('img');
				if (backgroundImg) {
					await new Promise((resolve) => {
						if (backgroundImg.complete) {
							resolve(true);
						} else {
							backgroundImg.onload = () => resolve(true);
						}
					});
				}

				// Configure html2canvas with proper settings
				const canvas = await html2canvas(bannerRef, {
					useCORS: true,
					allowTaint: true,
					backgroundColor: null,
					scale: 2, // Increase quality
					logging: false
				});

				return canvas.toDataURL('image/png');
			} catch (error) {
				console.error('Error generating image:', error);
				return null;
			}
		}
		return null;
	}
</script>

<div class="container mx-auto max-w-4xl px-4 py-12">
	<div class="mb-2">
		<a
			href="/{slug}"
			class="inline-flex items-center text-sm text-muted-foreground hover:text-primary"
		>
			<ArrowLeft class="mr-1 h-4 w-4" />
			Back to event
		</a>
	</div>

	<div class="mb-10 text-center">
		<h1 class="mb-4 text-4xl font-bold">Thank You for Registering!</h1>
		<p class="text-xl text-muted-foreground">
			We're excited to see you at {eventData.name} on {formatDisplayDate(eventData.date)}
		</p>
		<p class="mt-2 text-muted-foreground">
			A confirmation email has been sent to <span class="font-medium">{eventData.email}</span>
		</p>
	</div>

	<div class="grid gap-8 md:grid-cols-2">
		<Card class="p-6">
			<h2 class="mb-4 flex items-center text-2xl font-semibold">
				<Calendar class="mr-2 h-5 w-5" />
				Add to Calendar
			</h2>
			<p class="mb-4 text-muted-foreground">
				Don't miss the event! Add it to your preferred calendar.
			</p>
			<div class="grid grid-cols-1 gap-3 sm:grid-cols-2">
				<Button variant="outline" onclick={() => window.open(getGoogleCalendarUrl(), '_blank')}>
					<img
						src="{googlecalendar}?height=20&width=20"
						alt="Google Calendar"
						width={20}
						height={20}
						class="mr-2"
					/>
					Google Calendar
				</Button>
				<Button variant="outline" onclick={() => window.open(getAppleCalendarUrl(), '_blank')}>
					<img
						src="{applecalendar}?height=20&width=20"
						alt="Apple Calendar"
						width={20}
						height={20}
						class="mr-2"
					/>
					Apple Calendar
				</Button>
				<Button variant="outline" onclick={() => window.open(getOutlookCalendarUrl(), '_blank')}>
					<img
						src="{outlookcalendar}?height=20&width=20"
						alt="Outlook"
						width={20}
						height={20}
						class="mr-2"
					/>
					Outlook
				</Button>
				<Button variant="outline" onclick={() => window.open(getOffice365CalendarUrl(), '_blank')}>
					<img
						src="{office365calendar}?height=20&width=20"
						alt="Office 365"
						width={20}
						height={20}
						class="mr-2"
					/>
					Office 365
				</Button>
				<Button
					variant="outline"
					class="sm:col-span-2"
					onclick={() =>
						window.open(
							`mailto:?subject=${encodeURIComponent(`Invitation: ${eventData.name}`)}&body=${encodeURIComponent(`I'd like to invite you to ${eventData.name} on ${eventData.date} at ${eventData.location}. ${eventData.description}`)}`
						)}
				>
					<Mail class="mr-2 h-4 w-4" />
					Email to a Friend
				</Button>
			</div>
		</Card>

		<Card class="p-6">
			<h2 class="mb-4 flex items-center text-2xl font-semibold">
				<Share2 class="mr-2 h-5 w-5" />
				Share Your Excitement
			</h2>
			<p class="mb-4 text-muted-foreground">Let your network know you're attending!</p>

			<!-- <Tabs defaultValue="banner"> -->
			<Tabs value="banner">
				<TabsList class="mb-4 grid w-full grid-cols-2">
					<TabsTrigger value="banner">Create Banner</TabsTrigger>
					<TabsTrigger value="text">Share Text</TabsTrigger>
				</TabsList>

				<TabsContent value="banner">
					<div class="space-y-4">
						<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
							<div>
								<label for="user-name" class="mb-1 block text-sm font-medium"> Your Name </label>
								<Input
									id="user-name"
									bind:value={userName}
									placeholder="Enter your name"
									class="w-full"
								/>
							</div>
							<div>
								<label for="photo-upload-button" class="mb-1 block text-sm font-medium">
									Your Photo
								</label>
								<Button
									id="photo-upload-button"
									variant="outline"
									onclick={() => document.getElementById('photo-upload')?.click()}
									class="w-full"
								>
									<Upload class="mr-2 h-4 w-4" />
									{userImage ? 'Change Photo' : 'Add Your Photo'}
								</Button>
								<input
									id="photo-upload"
									type="file"
									accept="image/*"
									class="hidden"
									on:change={handleImageUpload}
								/>
							</div>
						</div>
					</div>
					<div
						bind:this={bannerRef}
						class="relative mt-4 flex h-64 w-full items-center justify-center overflow-hidden rounded-lg"
					>
						<!-- Background Banner of the Share Card -->
						<div class="absolute inset-0 flex items-center justify-center">
							<img
								src="{socialShare.share_image_url}?height=300&width=600"
								alt="Event banner"
								width={1600}
								height={1300}
								class="h-full w-full object-fill"
							/>
						</div>

						<div
							class="absolute inset-0 flex flex-col items-center justify-center bg-black/40 p-4 text-white"
						>
							<h3 class="mb-2 text-center text-2xl font-bold">{eventData.name}</h3>
							<p class="mb-2 text-center">
								{userName ? `${userName} will be there!` : "I'll be there!"} Will you?
							</p>

							{#if userImage}
								<div class="mb-2 h-20 w-20 overflow-hidden rounded-full border-2 border-white">
									<img
										src={userImage}
										alt="profile"
										width={80}
										height={80}
										class="h-full w-full object-cover"
									/>
								</div>
							{/if}
							{#if userName}
								<p class="text-center text-sm font-medium">{userName}</p>
							{/if}
						</div>
					</div>

					<!-- Social Share Buttons Start here -->

					<div class="mt-4 flex flex-wrap justify-center gap-2">
						<!-- <Button variant="outline" onclick={() => shareToSocial('facebook')}>
							<Facebook class="mr-2 h-4 w-4" />
							Facebook
						</Button>

						<Button variant="outline" onclick={() => shareToSocial('instagram')}>
							<Instagram class="mr-2 h-4 w-4" />
							Instagram
						</Button>

						<Button variant="outline" onclick={() => shareToSocial('twitter')}>
							<Twitter class="mr-2 h-4 w-4" />
							Twitter
						</Button>

						<Button variant="outline" onclick={() => shareToSocial('linkedin')}>
							<Linkedin class="mr-2 h-4 w-4" />
							LinkedIn
						</Button>

						<Button variant="outline" onclick={() => shareToSocial('whatsapp')}>
							<svg class="mr-2 h-4 w-4" viewBox="0 0 24 24">
								<path
									fill="currentColor"
									d="M12.031 6.172c-3.181 0-5.767 2.586-5.768 5.766-.001 1.298.38 2.27 1.019 3.287l-.582 2.128 2.182-.573c.978.58 1.911.928 3.145.929 3.178 0 5.767-2.587 5.768-5.766.001-3.187-2.575-5.77-5.764-5.771zm3.392 8.244c-.144.405-.837.774-1.17.824-.299.045-.677.063-1.092-.069-.252-.08-.575-.187-.988-.365-1.739-.751-2.874-2.502-2.961-2.617-.087-.116-.708-.94-.708-1.793s.448-1.273.607-1.446c.159-.173.346-.217.462-.217l.332.006c.106.005.249-.04.39.298.144.347.491 1.2.534 1.287.043.087.072.188.014.304-.058.116-.087.188-.173.289l-.26.304c-.087.086-.177.18-.076.354.101.174.449.741.964 1.201.662.591 1.221.774 1.394.86s.274.072.376-.043c.101-.116.433-.506.549-.68.116-.173.231-.145.39-.087s1.011.477 1.184.564.289.13.332.202c.045.072.045.419-.1.824zm-3.423-14.416c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm.029 18.88c-1.161 0-2.305-.292-3.318-.844l-3.677.964.984-3.595c-.607-1.052-.927-2.246-.926-3.468.001-3.825 3.113-6.937 6.937-6.937 1.856.001 3.598.723 4.907 2.034 1.31 1.311 2.031 3.054 2.03 4.908-.001 3.825-3.113 6.938-6.937 6.938z"
								/>
							</svg>
							WhatsApp
						</Button> -->

						<!-- <Button variant="outline" onclick={() => shareToSocial('telegram')}>
							<svg class="mr-2 h-4 w-4" viewBox="0 0 24 24">
								<path
									fill="currentColor"
									d="M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.894 8.221l-1.97 9.28c-.145.658-.537.818-1.084.508l-3-2.21-1.446 1.394c-.14.18-.357.295-.6.295-.002 0-.003 0-.005 0l.213-3.054 5.56-5.022c.24-.213-.054-.334-.373-.121l-6.869 4.326-2.96-.924c-.64-.203-.658-.64.135-.954l11.566-4.458c.538-.196 1.006.128.832.941z"
								/>
							</svg>
							Telegram
						</Button> -->

						<!-- <Button variant="outline" onclick={() => shareToSocial('email')}>
							<Mail class="mr-2 h-4 w-4" />
							Email
						</Button> -->

						<Button variant="outline" onclick={() => shareToSocial('download')}>Download</Button>
					</div>
				</TabsContent>

				<TabsContent value="text">
					<div class="space-y-4">
						<Textarea value={shareText} readonly rows={4} class="resize-none" />

						<Button onclick={copyToClipboard} class="w-full">
							{#if copied}
								<Check class="mr-2 h-4 w-4" />
								Copied!
							{:else}
								<Copy class="mr-2 h-4 w-4" />
								Copy to Clipboard
							{/if}
						</Button>

						<!-- <div class="mt-4 flex flex-wrap justify-center gap-2">
							<Button
								size="sm"
								variant="outline"
								onclick={() =>
									window.open(
										`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}&quote=${encodeURIComponent(shareText)}`,
										'_blank'
									)}
							>
								<Facebook class="mr-2 h-4 w-4" />
								Facebook
							</Button>
							<Button
								size="sm"
								variant="outline"
								onclick={() =>
									window.open(
										`https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}`,
										'_blank'
									)}
							>
								<Twitter class="mr-2 h-4 w-4" />
								Twitter
							</Button>
							<Button
								size="sm"
								variant="outline"
								onclick={() =>
									window.open(
										`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`,
										'_blank'
									)}
							>
								<Linkedin class="mr-2 h-4 w-4" />
								LinkedIn
							</Button>
						</div> -->
					</div>
				</TabsContent>
			</Tabs>
		</Card>
	</div>

	<div class="mt-10 text-center">
		<p class="text-muted-foreground">
			If you have any questions, please contact us at <span class="font-medium"
				><EMAIL></span
			>
		</p>
	</div>
</div>

<script lang="ts">
	import { onMount } from 'svelte';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { <PERSON>, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { analytics } from '$lib/analytics/analytics';
	import { errorTracker, trackedFetch } from '$lib/analytics/errorTracking';
	import { isPostHogAvailable } from '$lib/analytics/posthog';

	let testResults: string[] = [];
	let isAnalyticsReady = false;

	onMount(() => {
		// Check if PostHog is loaded
		setTimeout(() => {
			isAnalyticsReady = isPostHogAvailable();
			if (isAnalyticsReady) {
				addResult('✅ PostHog is loaded and ready');
			} else {
				addResult('❌ PostHog is not loaded. Check API key and configuration.');
			}
		}, 1000);
	});

	function addResult(message: string) {
		testResults = [...testResults, `${new Date().toLocaleTimeString()}: ${message}`];
	}

	function testPageView() {
		analytics.trackPageView('/analytics-test', {
			test_page: true,
			timestamp: new Date().toISOString()
		});
		addResult('📊 Page view tracked');
	}

	function testButtonClick() {
		analytics.trackButtonClick('test_button', 'analytics_test_page', {
			test_data: 'button_click_test'
		});
		addResult('🖱️ Button click tracked');
	}

	function testFormEvents() {
		analytics.trackFormStarted('test-event-id');
		addResult('📝 Form started tracked');
		
		analytics.trackFormFieldInteraction('test-event-id', 'test_field', 'text');
		addResult('📝 Form field interaction tracked');
		
		analytics.trackFormValidationError('test-event-id', 'test_field', 'Test validation error');
		addResult('❌ Form validation error tracked');
		
		analytics.trackFormSubmissionSuccess('test-event-id', 'test-registration-id');
		addResult('✅ Form submission success tracked');
	}

	function testAuthEvents() {
		analytics.trackLoginAttempt('<EMAIL>');
		addResult('🔐 Login attempt tracked');
		
		analytics.trackLoginSuccess('test-user-id');
		addResult('✅ Login success tracked');
		
		analytics.trackLogout();
		addResult('👋 Logout tracked');
	}

	function testAdminEvents() {
		analytics.trackAdminPageView('test-admin-page', 'test-event-id');
		addResult('👨‍💼 Admin page view tracked');
		
		analytics.trackEventCreated('test-event-id', 'Test Event');
		addResult('🎉 Event created tracked');
		
		analytics.trackEventPublished('test-event-id');
		addResult('📢 Event published tracked');
	}

	function testErrorTracking() {
		analytics.trackError('test_error', 'This is a test error', {
			test_context: 'analytics_testing'
		});
		addResult('🚨 Error tracked');
		
		errorTracker.trackAPIError('/test/api', 'POST', 500, 'Test API error');
		addResult('🌐 API error tracked');
	}

	function testPerformanceTracking() {
		analytics.trackPerformance('test_metric', 1500, {
			test_context: 'performance_testing'
		});
		addResult('⚡ Performance metric tracked');
	}

	async function testTrackedFetch() {
		try {
			// This will likely fail, but we want to test the error tracking
			await trackedFetch('/api/test-endpoint', {
				method: 'POST',
				body: JSON.stringify({ test: true })
			});
		} catch (error) {
			addResult('🌐 Tracked fetch error handling tested');
		}
	}

	function testEventPageInteractions() {
		analytics.trackEventPageView('test-event-id', 'test-slug', 'Test Event');
		addResult('📄 Event page view tracked');
		
		analytics.trackLinkClick('Test Link', '/test-url', 'test_location');
		addResult('🔗 Link click tracked');
	}

	function testGuestManagement() {
		analytics.trackGuestListViewed('test-event-id', 25);
		addResult('👥 Guest list viewed tracked');
		
		analytics.trackGuestDataExported('test-event-id', 'csv');
		addResult('📊 Guest data export tracked');
		
		analytics.trackQRCodeScanned('test-event-id', 'check_in');
		addResult('📱 QR code scan tracked');
	}

	function runAllTests() {
		testResults = [];
		addResult('🚀 Starting comprehensive analytics test...');
		
		testPageView();
		testButtonClick();
		testFormEvents();
		testAuthEvents();
		testAdminEvents();
		testErrorTracking();
		testPerformanceTracking();
		testTrackedFetch();
		testEventPageInteractions();
		testGuestManagement();
		
		addResult('✨ All tests completed! Check PostHog dashboard for events.');
	}

	function clearResults() {
		testResults = [];
	}
</script>

<svelte:head>
	<title>Analytics Testing - PostHog Integration</title>
</svelte:head>

<div class="container mx-auto p-8 max-w-4xl">
	<Card>
		<CardHeader>
			<CardTitle class="text-2xl">PostHog Analytics Testing</CardTitle>
			<p class="text-muted-foreground">
				Test all analytics events to ensure they're working correctly.
			</p>
		</CardHeader>
		<CardContent class="space-y-6">
			<!-- Status -->
			<div class="p-4 rounded-lg {isAnalyticsReady ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'} border">
				<p class="font-medium {isAnalyticsReady ? 'text-green-800' : 'text-red-800'}">
					Analytics Status: {isAnalyticsReady ? 'Ready' : 'Not Ready'}
				</p>
				{#if !isAnalyticsReady}
					<p class="text-sm text-red-600 mt-1">
						Make sure you have set the PostHog environment variables in your .env file:
						<br>
						<code>PUBLIC_POSTHOG_API_KEY=phc_MtdeHEDSmDcmQCPE420NjptCwvUUpS1wSYEtXlzBdn7</code>
						<br>
						<code>PUBLIC_POSTHOG_HOST=https://us.i.posthog.com</code>
					</p>
				{/if}
			</div>

			<!-- Test Controls -->
			<div class="grid grid-cols-2 md:grid-cols-3 gap-4">
				<Button onclick={testPageView} variant="outline">Test Page View</Button>
				<Button onclick={testButtonClick} variant="outline">Test Button Click</Button>
				<Button onclick={testFormEvents} variant="outline">Test Form Events</Button>
				<Button onclick={testAuthEvents} variant="outline">Test Auth Events</Button>
				<Button onclick={testAdminEvents} variant="outline">Test Admin Events</Button>
				<Button onclick={testErrorTracking} variant="outline">Test Error Tracking</Button>
				<Button onclick={testPerformanceTracking} variant="outline">Test Performance</Button>
				<Button onclick={testEventPageInteractions} variant="outline">Test Event Page</Button>
				<Button onclick={testGuestManagement} variant="outline">Test Guest Mgmt</Button>
			</div>

			<div class="flex gap-4">
				<Button onclick={runAllTests} class="flex-1">Run All Tests</Button>
				<Button onclick={clearResults} variant="outline">Clear Results</Button>
			</div>

			<!-- Test Results -->
			<div class="space-y-2">
				<h3 class="text-lg font-semibold">Test Results</h3>
				<div class="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
					{#if testResults.length === 0}
						<p class="text-gray-500 italic">No tests run yet. Click a test button above.</p>
					{:else}
						{#each testResults as result}
							<div class="text-sm font-mono mb-1">{result}</div>
						{/each}
					{/if}
				</div>
			</div>

			<!-- Instructions -->
			<div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
				<h4 class="font-medium text-blue-800 mb-2">How to Verify</h4>
				<ol class="text-sm text-blue-700 space-y-1 list-decimal list-inside">
					<li>Run the tests above</li>
					<li>Go to your PostHog dashboard at <a href="https://us.i.posthog.com" target="_blank" class="underline">https://us.i.posthog.com</a></li>
					<li>Navigate to Events → Live Events to see real-time events</li>
					<li>Check that all the test events appear with the correct properties</li>
					<li>Verify that user identification is working in the Persons section</li>
				</ol>
			</div>
		</CardContent>
	</Card>
</div>

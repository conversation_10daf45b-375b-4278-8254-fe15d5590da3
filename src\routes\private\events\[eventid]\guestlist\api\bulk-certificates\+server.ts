// src/routes/api/bulk-certificates/+server.js
import { json } from '@sveltejs/kit';
import { generateAndSendBulkCertificates } from '$lib/server/bulkCertificates';

export async function POST({ request }) {
    try {
        const { workshopId } = await request.json();
        console.log('workshopId received on API Server:', workshopId);

        if (!workshopId) {
            return new Response('Workshop ID is required', { status: 400 });
        }

        const results = await generateAndSendBulkCertificates(workshopId);

        return json({
            success: true,
            results
        });
    } catch (error) {
        console.error('Bulk certificate generation error:', error);
        return new Response(error.message, { status: 500 });
    }
}
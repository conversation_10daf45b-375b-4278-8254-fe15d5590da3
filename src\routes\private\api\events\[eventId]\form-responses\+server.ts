// API routes for admin access to form responses

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

// GET /private/api/events/[eventId]/form-responses (for admin)
export const GET: RequestHandler = async ({ params, locals: { supabase } }) => {
  try {
    const { eventId } = params;

    // Check if user is authenticated and is an admin
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin access
    const { data: adminData, error: adminError } = await supabase
      .from('admins')
      .select('id')
      .eq('auth_id', session.user.id)
      .single();

    if (adminError || !adminData) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify event exists and user has access
    const { data: eventData, error: eventError } = await supabase
      .from('events')
      .select('id, created_by')
      .eq('id', eventId)
      .single();

    if (eventError || !eventData) {
      return json({ error: 'Event not found' }, { status: 404 });
    }

    // Get form responses (this would be implemented in formResponses.ts)
    // For now, return a placeholder
    return json({ data: [] });

  } catch (error) {
    console.error('Error in GET /private/api/events/[eventId]/form-responses:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

// API endpoint to initialize standard fields for an event
// This ensures all standard fields exist, creating only missing ones

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { initializeStandardFields } from '$lib/server/formFields';

// POST /private/api/events/[eventId]/form-fields/initialize
export const POST: RequestHandler = async ({ params, locals: { supabase } }) => {
  try {
    const { eventId } = params;

    // Check if user is authenticated and is an admin
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin access
    const { data: adminData, error: adminError } = await supabase
      .from('admins')
      .select('id')
      .eq('auth_id', session.user.id)
      .single();

    if (adminError || !adminData) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify event exists and user has access
    const { data: eventData, error: eventError } = await supabase
      .from('events')
      .select('id, created_by')
      .eq('id', eventId)
      .single();

    if (eventError || !eventData) {
      return json({ error: 'Event not found' }, { status: 404 });
    }

    // Initialize standard fields (will only create missing ones due to ON CONFLICT DO NOTHING)
    const result = await initializeStandardFields(eventId);
    
    if (result.error) {
      return json({ error: result.error }, { status: 500 });
    }

    return json({ success: true, message: 'Standard fields initialized successfully' });
  } catch (error) {
    console.error('Error in POST /private/api/events/[eventId]/form-fields/initialize:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

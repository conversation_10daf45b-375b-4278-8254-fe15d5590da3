import { supabaseAdmin } from '$lib/server/db/supabaseAdmin.js';
import { redirect } from '@sveltejs/kit';


export async function GET({ setHeaders, locals: { safeGetSession }, params }) {
    const { session } = await safeGetSession();
    if (!session) {
        redirect(303, '/');
    }
    // TODO: Need to enter the correct felid name
    // const { data } = await supabaseAdmin.from('guests').select('name, email, mobile, organization');
    const { data } = await supabaseAdmin.from('guests').select('name, email, mobile, organization, status, check_in_date_time,check_out_date_time, identity_type, national_id, passport_number, nationality, date_of_birth, front_id_photo_url,back_id_photo_url, gender, nationality_form, business, category, stage, know, first_name, middle_name, last_name').eq('event_id', params.eventid);

    function escapeField(field) {
        if (field === null || field === undefined) {
            return '""';
        }

        field = String(field);

        // Handle Arabic text and other special characters
        field = field.normalize('NFD');

        // Escape quotes
        if (field.includes('"')) {
            field = field.replace(/"/g, '""');
        }

        // Always wrap fields in quotes to handle special characters better
        return `"${field}"`;
    }

    // Add BOM for Excel to recognize UTF-8
    const BOM = '\uFEFF';

    // Convert data to CSV format
    // const headers = ['Name', 'Email', 'Mobile', 'Organization']; // TODO: Need to enter the correct felid name
    const headers = ['Name', 'Email', 'Mobile', 'Organization', 'Status', 'Check In Date & Time', 'Check Out Date & Time', 'Identity Type', 'National ID', 'Passport Number', 'National', 'Date of Birth', 'Front ID Photo URL', 'Back ID Photo URL', 'Gender', 'Nationality Form', 'Business', 'Category', 'Stage', 'Know', 'First Name', 'Middle Name', 'Last Name'];
    const csv = BOM + [
        headers.map(escapeField).join(','),
        ...data.map((row) =>
            [row.name, row.email, row.mobile, row.organization, row.status, row.check_in_date_time, row.check_out_date_time, row.identity_type, row.national_id, row.passport_number, row.nationality, row.date_of_birth, row.front_id_photo_url, row.back_id_photo_url, row.gender, row.nationality_form, row.business, row.category, row.stage, row.know, row.first_name, row.middle_name, row.last_name]
                .map(escapeField)
                .join(',')
        )
    ].join('\n');

    // Set headers with UTF-8 encoding
    setHeaders({
        'Content-Type': 'text/csv; charset=utf-8',
        'Content-Disposition': 'attachment; filename="export.csv"'
    });

    // Return the CSV data as UTF-8
    return new Response(csv, {
        headers: {
            'Content-Type': 'text/csv; charset=utf-8'
        }
    });
}
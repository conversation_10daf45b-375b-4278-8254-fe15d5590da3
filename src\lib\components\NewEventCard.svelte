<script lang="ts">
	import * as Card from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Calendar, MapPin } from 'lucide-svelte';
	import { goto } from '$app/navigation';

	export let props;

	function formatDate(date: string) {
		const dateObject = new Date(date);
		const options: Intl.DateTimeFormatOptions = {
			year: 'numeric',
			month: 'long',
			day: 'numeric'
		};
		return dateObject.toLocaleDateString('en-US', options);
	}
</script>

<!-- <Card.Root class="overflow-hidden">
	<Card.Header class="p-0">
		<a href={`./${props.title_slug}`}>
			<img
				alt=""
				class="aspect-video w-full object-cover"
				height="200"
				src={props.image_url}
				width="400"
			/></a
		>
	</Card.Header>
	<Card.Content class="grid gap-2.5 p-4">
		<time class="flex items-center text-base text-muted-foreground">
			<Calendar class="mr-2 h-4 w-4" />
			
			{formatDate(props.start_date)}
		</time>
		<h3 class="text-lg font-bold"><a href={`./${props.title_slug}`}>{props.title}</a></h3>
		<p class="line-clamp-2 text-base text-muted-foreground">
			{props.short_description}
		</p>
		<div class="flex items-center gap-2 text-base text-muted-foreground">
			<MapPin class="h-4 w-4" />
			{props.location}
		</div>
	</Card.Content>
	<Card.Footer class="p-4 pt-0">
		<Button onclick={() => goto(`./${props.title_slug}`)} class="w-full">More Information</Button>
	</Card.Footer>
</Card.Root> -->
<Card.Root class="flex h-full flex-col">
	<Card.Header class="p-0">
		<a href={`./${props.title_slug}`}>
			<img
				alt=""
				class="aspect-video w-full object-cover"
				height="200"
				src={props.image_url}
				width="400"
			/>
		</a>
	</Card.Header>
	<Card.Content class="flex flex-grow flex-col p-4">
		<div class="flex-grow">
			<time class="mb-2.5 flex items-center text-base text-muted-foreground">
				<Calendar class="mr-2 h-4 w-4" />
				{formatDate(props.start_date)}
			</time>
			<h3 class="mb-2.5 text-lg font-bold">
				<a href={`./${props.title_slug}`}>{props.title}</a>
			</h3>
		</div>
		<div class="min-h-[3rem]">
			<!-- Ensures minimum height for description -->
			<p class="line-clamp-2 text-base text-muted-foreground">
				{props.short_description}
			</p>
		</div>
		<div class="mt-4">
			<div class="mb-4 flex items-center gap-2 text-base text-muted-foreground">
				<MapPin class="h-4 w-4" />
				{props.location}
			</div>
			<Button onclick={() => goto(`./${props.title_slug}`)} class="w-full">More Information</Button>
		</div>
	</Card.Content>
</Card.Root>

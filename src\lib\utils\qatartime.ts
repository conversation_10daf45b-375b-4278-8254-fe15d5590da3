export async function getFormattedQatarTime() {
    const now = new Date();
    const qatarTime = new Date(now.toLocaleString('en-US', { timeZone: 'Asia/Qatar' }));
    const formattedQatarTime =
        qatarTime.getFullYear() +
        '-' +
        ('0' + (qatarTime.getMonth() + 1)).slice(-2) +
        '-' +
        ('0' + qatarTime.getDate()).slice(-2) +
        ' ' +
        ('0' + qatarTime.getHours()).slice(-2) +
        ':' +
        ('0' + qatarTime.getMinutes()).slice(-2) +
        ':' +
        ('0' + qatarTime.getSeconds()).slice(-2);

    return formattedQatarTime;
}


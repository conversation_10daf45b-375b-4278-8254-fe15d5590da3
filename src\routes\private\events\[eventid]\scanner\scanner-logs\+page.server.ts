import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals: { supabase } }) => {
    // Get today's date in ISO format (YYYY-MM-DD)
    const today = new Date().toISOString().split('T')[0];

    // Fetch valid scans
    const { data: scans, error: scansError } = await supabase
        .from('scans')
        .select('*')
        .gte('timestamp', today)
        .lt('timestamp', today + 'T23:59:59.999Z')
        .order('timestamp', { ascending: false });

    // Fetch invalid scans
    const { data: invalidScans, error: invalidScansError } = await supabase
        .from('invalid_scans')
        .select('*')
        .gte('timestamp', today)
        .lt('timestamp', today + 'T23:59:59.999Z')
        .order('timestamp', { ascending: false });

    if (scansError) {
        console.error('Error fetching scans:', scansError);
    }

    if (invalidScansError) {
        console.error('Error fetching invalid scans:', invalidScansError);
    }



    return {
        scans: scans || [],
        invalidScans: invalidScans || []
    };
};
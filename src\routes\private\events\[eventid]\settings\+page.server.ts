import { redirect, error, type Actions } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals: { supabase, safeGetSession } }) => {
	const { session } = await safeGetSession();
	if (!session) {
		redirect(303, '/login');
	}

	const { eventid } = params;

	// Get event settings
	const { data: eventData, error: eventError } = await supabase
		.from('events')
		.select('id, title, is_published, is_private, registration_open, max_registrations, waiting_list, auto_approve_waitlist, active_code')
		.eq('id', eventid)
		.single();

	if (eventError || !eventData) {
		throw error(404, 'Event not found');
	}

	return {
		event: eventData
	};
};

export const actions: Actions = {
	updateSettings: async ({ params, request, locals: { supabase, safeGetSession } }) => {
		const { session } = await safeGetSession();
		if (!session) {
			redirect(303, '/login');
		}

		const { eventid } = params;
		const formData = await request.formData();

		// Extract form data
		const isPublished = formData.get('is_published') === 'on';
		const isPrivate = formData.get('is_private') === 'on';
		const registrationOpen = formData.get('registration_open') === 'on';
		const waitingList = formData.get('waiting_list') === 'on';
		const autoApproveWaitlist = formData.get('auto_approve_waitlist') === 'on';
		const maxRegistrations = formData.get('max_registrations') as string;
		const activeCode = formData.get('active_code') as string;

		// Prepare update data
		const updateData: any = {
			is_published: isPublished,
			is_private: isPrivate,
			registration_open: registrationOpen,
			waiting_list: waitingList,
			auto_approve_waitlist: autoApproveWaitlist,
			max_registrations: maxRegistrations ? parseInt(maxRegistrations) : null,
			active_code: activeCode || null
		};

		// Update event settings
		const { error: updateError } = await supabase
			.from('events')
			.update(updateData)
			.eq('id', eventid);

		if (updateError) {
			console.error('Error updating event settings:', updateError);
			throw error(500, 'Failed to update event settings');
		}

		return { success: true };
	},

	reopenRegistration: async ({ params, locals: { supabase, safeGetSession } }) => {
		const { session } = await safeGetSession();
		if (!session) {
			redirect(303, '/login');
		}

		const { eventid } = params;

		// Manually reopen registration (admin override)
		const { error: updateError } = await supabase
			.from('events')
			.update({ registration_open: true })
			.eq('id', eventid);

		if (updateError) {
			console.error('Error reopening registration:', updateError);
			throw error(500, 'Failed to reopen registration');
		}

		return { success: true, message: 'Registration reopened successfully' };
	}
};

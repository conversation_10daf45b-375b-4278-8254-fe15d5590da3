// src/routes/api/certificate/+server.js
import { json } from '@sveltejs/kit';
import { generateCertificatePDF } from '$lib/server/certificate.js';

export async function POST({ request }) {
    try {
        const certificateData = await request.json();

        // Validate required fields
        if (!certificateData.recipientName || !certificateData.courseName) {
            return new Response('Missing required fields', { status: 400 });
        }

        // Generate PDF
        const pdfBuffer = await generateCertificatePDF(certificateData);

        // Return PDF with appropriate headers
        return new Response(pdfBuffer, {
            headers: {
                'Content-Type': 'application/pdf',
                'Content-Disposition': `attachment; filename="${certificateData.recipientName}-certificate.pdf"`,
            },
        });
    } catch (error) {
        console.error('Error generating certificate:', error);
        return new Response('Error generating certificate', { status: 500 });
    }
}
<script lang="ts">
	import type { PageProps } from './$types';
	import { Button } from '$lib/components/ui/button';
	import {
		Card,
		CardContent,
		CardDescription,
		CardFooter,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '$lib/components/ui/tabs';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Mail, Clock, Eye } from 'lucide-svelte';

	let activePreview: string | null = null;

	const { data, form }: PageProps = $props();

	let registrationSubject = $state(data.registrationTemplate?.subject);
	let registrationFromName = $state(data.registrationTemplate?.from_name);
	let registrationContent = $state(data.registrationTemplate?.content);

	let reminderSubject = $state(data.reminderTemplate?.subject);
	let reminderFromName = $state(data.reminderTemplate?.from_name);
	let reminderContent = $state(data.reminderTemplate?.content);

	function handlePreview(templateType: string) {
		activePreview = activePreview === templateType ? null : templateType;
	}

	function getDefaultTemplate(type: string): string {
		if (type === 'registration') {
			return `Hello {name},
  
  Thank you for registering with {company}!
  
  Your account has been successfully created. You can now access all of our services by clicking the link below:
  
  {link}
  
  If you have any questions, please don't hesitate to contact our support team.
  
  Best regards,
  The {company} Team`;
		} else {
			return `Hello {name},
  
  This is a friendly reminder that you have a pending action that requires your attention.
  
  Please log in to your account and complete the required steps by clicking the link below:
  
  {link}
  
  If you've already completed this action, please disregard this message.
  
  Thank you,
  The {company} Team`;
		}
	}
</script>

<div class="container mx-auto space-y-8 py-10">
	<div class="flex flex-col space-y-2">
		<h1 class="text-3xl font-bold tracking-tight">Email Templates</h1>
		<p class="text-muted-foreground">Customize the email templates sent to your users.</p>
	</div>

	<Tabs value="registration" class="w-full">
		<TabsList class="mb-8 grid w-full grid-cols-2">
			<TabsTrigger value="registration" class="flex items-center gap-2">
				<Mail class="h-4 w-4" />
				<span>Registration Email</span>
			</TabsTrigger>
			<TabsTrigger value="reminder" class="flex items-center gap-2">
				<Clock class="h-4 w-4" />
				<span>Reminder Email</span>
			</TabsTrigger>
		</TabsList>

		<TabsContent value="registration">
			<!-- Registration Email Template Form -->
			<div class="w-auto">
				<form method="POST">
					<Card class="md:col-span-3">
						<CardHeader>
							<CardTitle>Registration Email Template</CardTitle>
							<CardDescription
								>This email is sent to users when they register for your service.</CardDescription
							>
						</CardHeader>
						<CardContent class="space-y-6">
							<div class="space-y-2">
								<Label for="registration-subject">Subject Line</Label>
								<Input
									id="registration-subject"
									name="registration-subject"
									placeholder="Enter email subject line"
									bind:value={registrationSubject}
								/>
							</div>

							<div class="space-y-2">
								<Label for="registration-sender">Sender Name</Label>
								<Input
									id="registration-sender"
									name="registration-sender"
									placeholder="Enter sender name"
									bind:value={registrationFromName}
								/>
							</div>

							<div class="space-y-2">
								<Label for="registration-content">Email Content</Label>
								<Textarea
									id="registration-content"
									name="registration-content"
									placeholder="Enter email content"
									class="min-h-[300px] font-mono"
									bind:value={registrationContent}
								/>
								<!-- value={getDefaultTemplate('registration')} -->
								<p class="text-sm text-muted-foreground">
									You can use variables like {'{name}'}, {'{company}'}, and {'{link}'} which will be
									replaced with actual values.
								</p>
							</div>
						</CardContent>
						<CardFooter class="flex justify-between">
							<Button variant="outline">Cancel</Button>
							<div class="flex gap-2">
								<Button
									variant="outline"
									onclick={() => handlePreview('registration')}
									class="flex items-center gap-2"
								>
									<Eye class="h-4 w-4" />
									Preview
								</Button>
								<Button type="submit" formaction="?/saveRegistration">Save Template</Button>
							</div>
						</CardFooter>
					</Card>
				</form>

				{#if activePreview === 'registration'}
					<Card class="md:col-span-2">
						<CardHeader>
							<CardTitle>Email Preview</CardTitle>
							<CardDescription>Preview how your email will look to recipients</CardDescription>
						</CardHeader>
						<CardContent>
							<div class="space-y-4 rounded-md border p-4">
								<div class="border-b pb-2">
									<div class="font-medium">Welcome to Our Service!</div>
									<div class="text-sm text-muted-foreground">From: Your Company Name</div>
								</div>
								<div class="prose prose-sm max-w-none">
									{@html getDefaultTemplate('registration').replace(/\n/g, '<br>')}
								</div>
							</div>
						</CardContent>
					</Card>
				{/if}
			</div>
		</TabsContent>

		<TabsContent value="reminder">
			<!-- Reminder Email Template Form -->
			<div class="w-auto">
				<form method="POST">
					<Card class="md:col-span-3">
						<CardHeader>
							<CardTitle>Reminder Email Template</CardTitle>
							<CardDescription
								>This email is sent to remind users about pending actions or upcoming events.</CardDescription
							>
						</CardHeader>
						<CardContent class="space-y-6">
							<div class="space-y-2">
								<Label for="reminder-subject">Subject Line</Label>
								<Input
									id="reminder-subject"
									name="reminder-subject"
									placeholder="Enter email subject line"
									value="Reminder: Action Required"
								/>
							</div>

							<div class="space-y-2">
								<Label for="reminder-sender">Sender Name</Label>
								<Input
									id="reminder-sender"
									name="reminder-sender"
									placeholder="Enter sender name"
									value="Your Company Name"
								/>
							</div>

							<div class="space-y-2">
								<Label for="reminder-content">Email Content</Label>
								<Textarea
									id="reminder-content"
									name="reminder-content"
									placeholder="Enter email content"
									class="min-h-[300px] font-mono"
									value={getDefaultTemplate('reminder')}
								/>
								<p class="text-sm text-muted-foreground">
									You can use variables like {'{name}'}, {'{company}'}, and {'{link}'} which will be
									replaced with actual values.
								</p>
							</div>
						</CardContent>
						<CardFooter class="flex justify-between">
							<Button variant="outline">Cancel</Button>
							<div class="flex gap-2">
								<Button
									variant="outline"
									onclick={() => handlePreview('reminder')}
									class="flex items-center gap-2"
								>
									<Eye class="h-4 w-4" />
									Preview
								</Button>
								<Button type="submit" formaction="?/saveReminder">Save Template</Button>
							</div>
						</CardFooter>
					</Card>
				</form>

				{#if activePreview === 'reminder'}
					<Card class="md:col-span-2">
						<CardHeader>
							<CardTitle>Email Preview</CardTitle>
							<CardDescription>Preview how your email will look to recipients</CardDescription>
						</CardHeader>
						<CardContent>
							<div class="space-y-4 rounded-md border p-4">
								<div class="border-b pb-2">
									<div class="font-medium">Reminder: Action Required</div>
									<div class="text-sm text-muted-foreground">From: Your Company Name</div>
								</div>
								<div class="prose prose-sm max-w-none">
									{@html getDefaultTemplate('reminder').replace(/\n/g, '<br>')}
								</div>
							</div>
						</CardContent>
					</Card>
				{/if}
			</div>
		</TabsContent>
	</Tabs>
</div>

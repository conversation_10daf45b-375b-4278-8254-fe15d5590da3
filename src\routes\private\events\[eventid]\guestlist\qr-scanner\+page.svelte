<script lang="ts">
	import type { DetectedBarcode, BarcodeFormat } from 'barcode-detector/pure';
	import QrcodeStream from '$lib/components/qrcode-stream.svelte';
	// import Fullscreen from 'lucide-svelte/icons/fullscreen';
	import fullScreen from '$lib/images/qrscanner/fullscreen.svg';
	import fullScreenExit from '$lib/images/qrscanner/fullscreen-exit.svg';
	import checkmark from '$lib/images/qrscanner/checkmark.svg';
	import { supabase } from '$lib/db/supabaseClient';

	let props = $props();

	let qrDataArray = props.data.QRData;
	let result = $state('');
	let error = $state('');
	let paused = $state(false);
	let showScanConfirmation = $state(false);
	let emailFound = $state(false);

	let fullscreen = $state(false);
	let wrapper: HTMLDivElement;

	let fullscreenIcon = $derived(fullscreen ? fullScreenExit : fullScreen);

	let isValid = $state<boolean | undefined>(undefined);
	let validationPending = $derived(isValid === undefined && paused);
	let validationSuccess = $derived(isValid === true);
	let validationFailure = $derived(isValid === false);

	let selectedConstraints = $state({ facingMode: 'environment' });
	let barcodeFormats: {
		[key in BarcodeFormat]: boolean;
	} = $state({
		aztec: false,
		code_128: false,
		code_39: false,
		code_93: false,
		codabar: false,
		databar: false,
		databar_expanded: false,
		databar_limited: false,
		data_matrix: false,
		dx_film_edge: false,
		ean_13: false,
		ean_8: false,
		itf: false,
		maxi_code: false,
		micro_qr_code: false,
		pdf417: false,
		qr_code: true,
		rm_qr_code: false,
		upc_a: false,
		upc_e: false,
		linear_codes: false,
		matrix_codes: false,
		unknown: false
	});

	// computed value for selected formats
	let selectedBarcodeFormats: BarcodeFormat[] = $derived(
		Object.keys(barcodeFormats).filter(
			// @ts-expect-error fix this later :)
			(format: string) => barcodeFormats[format]
		) as BarcodeFormat[]
	);

	// track function options
	const trackFunctionOptions = [
		{ text: 'nothing (default)', value: undefined },
		{ text: 'outline', value: paintOutline },
		{ text: 'centered text', value: paintCenterText },
		{ text: 'bounding box', value: paintBoundingBox }
	];

	let trackFunctionSelected = $state(trackFunctionOptions[1]);

	// camera constraint options
	// eslint-disable-next-line no-undef
	const defaultConstraintOptions: { label: string; constraints: MediaTrackConstraints }[] = [
		{ label: 'rear camera', constraints: { facingMode: 'environment' } },
		{ label: 'front camera', constraints: { facingMode: 'user' } }
	];

	let constraintOptions = $state(defaultConstraintOptions);

	function onError(err: { name: string; message: string }) {
		error = `[${err.name}]: `;

		if (err.name === 'NotAllowedError') {
			error += 'you need to grant camera access permission';
		} else if (err.name === 'NotFoundError') {
			error += 'no camera on this device';
		} else if (err.name === 'NotSupportedError') {
			error += 'secure context required (HTTPS, localhost)';
		} else if (err.name === 'NotReadableError') {
			error += 'is the camera already in use?';
		} else if (err.name === 'OverconstrainedError') {
			error += 'installed cameras are not suitable';
		} else if (err.name === 'StreamApiNotSupportedError') {
			error += 'Stream API is not supported in this browser';
		} else {
			error += err.message;
		}
	}

	// track functions
	function paintOutline(
		detectedCodes: {
			cornerPoints: { x: number; y: number }[];
			boundingBox: DOMRectReadOnly;
			rawValue: string;
			format: Exclude<BarcodeFormat, 'linear_codes' | 'matrix_codes'>;
		}[],
		ctx: CanvasRenderingContext2D
	) {
		for (const detectedCode of detectedCodes) {
			const [firstPoint, ...otherPoints] = detectedCode.cornerPoints;

			ctx.strokeStyle = 'red';
			ctx.beginPath();
			ctx.moveTo(firstPoint.x, firstPoint.y);

			for (const { x, y } of otherPoints) {
				ctx.lineTo(x, y);
			}

			ctx.lineTo(firstPoint.x, firstPoint.y);
			ctx.closePath();
			ctx.stroke();
		}
	}

	function paintBoundingBox(
		detectedCodes: {
			cornerPoints: { x: number; y: number }[];
			boundingBox: DOMRectReadOnly;
			rawValue: string;
			format: Exclude<BarcodeFormat, 'linear_codes' | 'matrix_codes'>;
		}[],
		ctx: CanvasRenderingContext2D
	) {
		for (const detectedCode of detectedCodes) {
			const {
				boundingBox: { x, y, width, height }
			} = detectedCode;

			ctx.lineWidth = 2;
			ctx.strokeStyle = '#007bff';
			ctx.strokeRect(x, y, width, height);
		}
	}

	function paintCenterText(
		detectedCodes: {
			cornerPoints: { x: number; y: number }[];
			boundingBox: DOMRectReadOnly;
			rawValue: string;
			format: Exclude<BarcodeFormat, 'linear_codes' | 'matrix_codes'>;
		}[],
		ctx: CanvasRenderingContext2D
	) {
		for (const detectedCode of detectedCodes) {
			const { boundingBox, rawValue } = detectedCode;

			const centerX = boundingBox.x + boundingBox.width / 2;
			const centerY = boundingBox.y + boundingBox.height / 2;

			const fontSize = Math.max(12, (50 * boundingBox.width) / ctx.canvas.width);

			ctx.font = `bold ${fontSize}px sans-serif`;
			ctx.textAlign = 'center';

			ctx.lineWidth = 3;
			ctx.strokeStyle = '#35495e';
			ctx.strokeText(detectedCode.rawValue, centerX, centerY);

			ctx.fillStyle = '#5cb984';
			ctx.fillText(rawValue, centerX, centerY);
		}
	}

	function onFullscreenChange() {
		// this becomes important when the user doesn't use the button to exit
		// fullscreen but hits ESC on desktop, pushes a physical back button on
		// mobile etc.
		fullscreen = document.fullscreenElement !== null;
	}

	function requestFullscreen() {
		const elem = wrapper;

		if (elem.requestFullscreen) {
			elem.requestFullscreen();
			// @ts-expect-error false positive
		} else if (elem.mozRequestFullScreen) {
			/* Firefox */
			// @ts-expect-error false positive
			elem.mozRequestFullScreen();
			// @ts-expect-error false positive
		} else if (elem.webkitRequestFullscreen) {
			/* Chrome, Safari and Opera */
			// @ts-expect-error false positive
			elem.webkitRequestFullscreen();
			// @ts-expect-error false positive
		} else if (elem.msRequestFullscreen) {
			/* IE/Edge */
			// @ts-expect-error false positive
			elem.msRequestFullscreen();
		}
	}

	function exitFullscreen() {
		if (document.exitFullscreen) {
			document.exitFullscreen();
			// @ts-expect-error false positive
		} else if (document.mozCancelFullScreen) {
			/* Firefox */
			// @ts-expect-error false positive
			document.mozCancelFullScreen();
			// @ts-expect-error false positive
		} else if (document.webkitExitFullscreen) {
			/* Chrome, Safari and Opera */
			// @ts-expect-error false positive
			document.webkitExitFullscreen();
			// @ts-expect-error false positive
		} else if (document.msExitFullscreen) {
			/* IE/Edge */
			// @ts-expect-error false positive
			document.msExitFullscreen();
		}
	}

	async function onCameraReady() {
		try {
			const devices = await navigator.mediaDevices.enumerateDevices();
			const videoDevices = devices.filter(({ kind }) => kind === 'videoinput');

			constraintOptions = [
				...defaultConstraintOptions,
				...videoDevices.map(({ deviceId, label }) => ({
					label: `${label} (ID: ${deviceId})`,
					constraints: { deviceId }
				}))
			];

			error = '';
		} catch (e) {
			console.error(e);
		}
	}

	function timeout(ms: number) {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	function onCameraOn() {
		showScanConfirmation = false;
	}

	function onCameraOff() {
		showScanConfirmation = true;
	}

	function resetValidationState() {
		isValid = undefined;
	}

	async function onDetect(detectedCodes: DetectedBarcode[]) {
		console.log('Detected Codes: ', detectedCodes);
		// result = JSON.stringify(detectedCodes.map((code) => code.rawValue));

		// console.log('Resut: ', result);

		// paused = true;
		// await timeout(500);
		// paused = false;

		// Adding logic here
		paused = true;

		// Read the QR code
		result = detectedCodes.map((code) => code.rawValue).toString();
		console.log('Raw Value: ', result);

		// Find the email from the List
		const validationStatus = checkEmail(result);
		console.log('Validation Status: ', validationStatus);

		if (validationStatus) {
			isValid = undefined;
			// Change the status in the DB
			const { data, error } = await supabase
				.from('guests')
				.update({ status: 'checked-in', check_in_date_time: new Date() })
				.eq('email', result)
				.eq('event_id', props.data.params.eventid);

			if (error) {
				console.error('Error updating the status: ', error);
			}
			console.log('Data: ', data);

			await timeout(2000);
			isValid = true;
		} else {
			isValid = undefined;

			await timeout(2000);
			isValid = false;
		}

		await timeout(2000);
		paused = false;
	}

	function checkEmail(email: string) {
		const emails = props.data.QRData.map((item) => item.email.toLowerCase());
		emailFound = emails.includes(email.toLowerCase());
		return emailFound;
	}

	$effect(() => {
		if (fullscreen) {
			requestFullscreen();
		} else {
			exitFullscreen();
		}
	});
</script>

<h2>QR SCanner</h2>

<p>
	Select cameras front or rear :

	<select bind:value={selectedConstraints}>
		{#each constraintOptions as option}
			<option value={option.constraints}>
				{option.label}
			</option>
		{/each}
	</select>
</p>

<p>
	Detected codes are visually highlighted in real-time. Use the following dropdown to change the
	flavor:

	<select bind:value={trackFunctionSelected}>
		{#each trackFunctionOptions as option}
			<option value={option}>
				{option.text}
			</option>
		{/each}
	</select>
</p>

<p>
	By default only QR-codes are detected but a variety of other barcode formats are also supported.
	You can select one or multiple but the more you select the more expensive scanning becomes:
</p>

{#each Object.keys(barcodeFormats) as option}
	{@const barcodeOption = option as BarcodeFormat}
	<span class="barcode-format-checkbox">
		<input type="checkbox" id={option} bind:checked={barcodeFormats[barcodeOption]} />
		<label for={option}>{option}</label>
	</span>
{/each}

{#if error}
	<div class="error">{error}</div>
{/if}

<div>
	Last result: <b>{result}</b>
</div>

<div class="scanner">
	<div class:fullscreen bind:this={wrapper} onfullscreenchange={onFullscreenChange}>
		<QrcodeStream
			constraints={selectedConstraints}
			track={trackFunctionSelected.value}
			formats={selectedBarcodeFormats}
			{onError}
			{onDetect}
			onCameraOn={resetValidationState}
			{onCameraOff}
			{paused}
		>
			<!-- 	onCameraOn={onCameraReady} // This was the code for above the onCameraOn function. to check all the availabe devies and use it. but its not incorporated -->
			<!-- {#if showScanConfirmation}
				{console.log('showScanConfirmation')}
				<div class="scan-confirmation">
					<img src={checkmark} alt="Checkmark" width="128" />
				</div>
			{/if} -->
			{#if validationSuccess}
				<div class="validation-success">Validation Successuful</div>
			{/if}

			{#if validationFailure}
				<div class="validation-failure">This is NOT a Valid QR Code!</div>
			{/if}

			{#if validationPending}
				<div class="validation-pending">Validation in progress...</div>
			{/if}

			<button onclick={() => (fullscreen = !fullscreen)} class="fullscreen-button">
				<img src={fullscreenIcon} alt="toggle fullscreen" />
			</button></QrcodeStream
		>
	</div>
</div>

<!-- <pre>
	{JSON.stringify(props.data.params.eventid, null, 2)}
</pre> -->

<style>
	.error {
		font-weight: bold;
		color: red;
	}

	.barcode-format-checkbox {
		margin-right: 10px;
		white-space: nowrap;
		display: inline-block;
	}

	.scanner {
		width: 100%;
		max-width: 600px;
		aspect-ratio: 4/3;
		margin-top: 20px;
	}

	.fullscreen {
		position: fixed;
		z-index: 1000;
		top: 0;
		bottom: 0;
		right: 0;
		left: 0;
	}

	.fullscreen-button {
		position: absolute;
		bottom: 0;
		right: 0;
		margin: 1rem;
	}
	.scan-confirmation {
		position: absolute;
		width: 100%;
		height: 100%;
		background-color: rgba(255, 255, 255, 0.8);
		display: flex;
		flex-flow: row nowrap;
		justify-content: center;
	}

	.validation-success,
	.validation-failure,
	.validation-pending {
		position: absolute;
		width: 100%;
		height: 100%;
		background-color: rgba(255, 255, 255, 0.8);
		padding: 10px;
		text-align: center;
		font-weight: bold;
		font-size: 1.4rem;
		color: black;
		display: flex;
		flex-flow: column nowrap;
		justify-content: center;
	}

	.validation-success {
		color: green;
	}

	.validation-failure {
		color: red;
	}
</style>

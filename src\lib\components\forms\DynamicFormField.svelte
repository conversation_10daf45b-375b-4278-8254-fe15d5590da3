<script lang="ts">
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';

	import { Checkbox } from '$lib/components/ui/checkbox';
	import { RadioGroup, RadioGroupItem } from '$lib/components/ui/radio-group';
	import { Badge } from '$lib/components/ui/badge';
	import { AlertCircle } from 'lucide-svelte';
	import type { FormField } from '$lib/types/formFields';

	// Props
	export let field: FormField;
	export let value: any;
	export let errors: string[] = [];
	export let onChange: (value: any) => void;

	// Reactive values
	$: validationAttributes = getValidationAttributes(field);
	$: hasErrors = errors.length > 0;
	$: fieldId = `field-${field.fieldKey}`;

	// Client-side validation attributes function
	function getValidationAttributes(field: FormField): Record<string, any> {
		const attributes: Record<string, any> = {};

		if (field.isRequired) {
			attributes.required = true;
		}

		if (field.placeholderText) {
			attributes.placeholder = field.placeholderText;
		}

		switch (field.fieldType) {
			case 'email':
				attributes.type = 'email';
				break;
			case 'phone':
				attributes.type = 'tel';
				break;
			case 'url':
				attributes.type = 'url';
				break;
			case 'number':
				attributes.type = 'number';
				break;
			case 'date':
				attributes.type = 'date';
				break;
			default:
				attributes.type = 'text';
		}

		if (field.validationRules) {
			const rules = field.validationRules;

			if (rules.minLength !== undefined) {
				attributes.minlength = rules.minLength;
			}

			if (rules.maxLength !== undefined) {
				attributes.maxlength = rules.maxLength;
			}

			if (rules.pattern) {
				attributes.pattern = rules.pattern;
			}

			if (field.fieldType === 'number') {
				if (rules.min !== undefined) {
					attributes.min = rules.min;
				}
				if (rules.max !== undefined) {
					attributes.max = rules.max;
				}
			}
		}

		return attributes;
	}

	// Handle input changes
	function handleInputChange(event: Event) {
		const target = event.target as HTMLInputElement;
		onChange(target.value);
	}

	function handleTextareaChange(event: Event) {
		const target = event.target as HTMLTextAreaElement;
		onChange(target.value);
	}

	function handleSelectChange(selectedValue: string) {
		onChange(selectedValue);
	}

	function handleCheckboxChange(checked: boolean) {
		// Single checkbox
		onChange(checked);
	}

	function handleMultipleCheckboxChange(optionValue: string, checked: boolean) {
		const currentValues = Array.isArray(value) ? [...value] : [];

		if (checked) {
			if (!currentValues.includes(optionValue)) {
				currentValues.push(optionValue);
			}
		} else {
			const index = currentValues.indexOf(optionValue);
			if (index > -1) {
				currentValues.splice(index, 1);
			}
		}

		onChange(currentValues);
	}

	function handleRadioChange(selectedValue: string) {
		onChange(selectedValue);
	}
</script>

<div class="space-y-2">
	<!-- Field Label -->
	<div class="flex items-center gap-2">
		<Label for={fieldId} class="text-sm font-medium">
			{field.fieldLabel}
			{#if field.isRequired}
				<span class="ml-1 text-red-500">*</span>
			{/if}
		</Label>
		{#if field.fieldType !== 'text'}
			<Badge variant="outline" class="text-xs">
				{field.fieldType}
			</Badge>
		{/if}
	</div>

	<!-- Help Text -->
	{#if field.helpText}
		<p class="text-sm text-muted-foreground">{field.helpText}</p>
	{/if}

	<!-- Field Input -->
	<div class="space-y-2">
		{#if field.fieldType === 'text' || field.fieldType === 'email' || field.fieldType === 'phone' || field.fieldType === 'url' || field.fieldType === 'number'}
			<Input
				id={fieldId}
				{...validationAttributes}
				{value}
				oninput={handleInputChange}
				class={hasErrors ? 'border-red-500 focus-visible:ring-red-500' : ''}
				aria-invalid={hasErrors}
				aria-describedby={hasErrors ? `${fieldId}-error` : undefined}
			/>
		{:else if field.fieldType === 'textarea'}
			<Textarea
				id={fieldId}
				{value}
				placeholder={field.placeholderText}
				oninput={handleTextareaChange}
				class={hasErrors ? 'border-red-500 focus-visible:ring-red-500' : ''}
				aria-invalid={hasErrors}
				aria-describedby={hasErrors ? `${fieldId}-error` : undefined}
				rows={3}
			/>
		{:else if field.fieldType === 'date'}
			<Input
				id={fieldId}
				type="date"
				{value}
				oninput={handleInputChange}
				class={hasErrors ? 'border-red-500 focus-visible:ring-red-500' : ''}
				aria-invalid={hasErrors}
				aria-describedby={hasErrors ? `${fieldId}-error` : undefined}
			/>
		{:else if field.fieldType === 'select'}
			<select
				id={fieldId}
				{value}
				on:change={(e) => handleSelectChange((e.target as HTMLSelectElement).value)}
				class={`w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 ${hasErrors ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'}`}
				aria-invalid={hasErrors}
				aria-describedby={hasErrors ? `${fieldId}-error` : undefined}
			>
				<option value="">{field.placeholderText || `Select ${field.fieldLabel}`}</option>
				{#if field.fieldOptions?.options}
					{#each field.fieldOptions.options as option}
						<option value={option}>{option}</option>
					{/each}
				{/if}
			</select>
		{:else if field.fieldType === 'checkbox'}
			{#if field.fieldOptions?.multiple && field.fieldOptions?.options}
				<!-- Multiple checkboxes -->
				<div class="space-y-3">
					{#each field.fieldOptions.options as option}
						{@const isChecked = Array.isArray(value) && value.includes(option)}
						<div class="flex items-center space-x-2">
							<Checkbox
								id="{fieldId}-{option}"
								checked={isChecked}
								onCheckedChange={(checked: boolean) =>
									handleMultipleCheckboxChange(option, checked)}
								aria-describedby={hasErrors ? `${fieldId}-error` : undefined}
							/>
							<Label for="{fieldId}-{option}" class="cursor-pointer text-sm font-normal">
								{option}
							</Label>
						</div>
					{/each}
				</div>
			{:else if field.fieldOptions?.options}
				<!-- Radio buttons (single selection from options) -->
				<RadioGroup value={value || ''} onValueChange={handleRadioChange} class="space-y-2">
					{#each field.fieldOptions.options as option}
						<div class="flex items-center space-x-2">
							<RadioGroupItem
								value={option}
								id="{fieldId}-{option}"
								aria-describedby={hasErrors ? `${fieldId}-error` : undefined}
							/>
							<Label for="{fieldId}-{option}" class="cursor-pointer text-sm font-normal">
								{option}
							</Label>
						</div>
					{/each}
				</RadioGroup>
			{:else}
				<!-- Single checkbox -->
				<div class="flex items-center space-x-2">
					<Checkbox
						id={fieldId}
						checked={value === true}
						onCheckedChange={handleCheckboxChange}
						aria-describedby={hasErrors ? `${fieldId}-error` : undefined}
					/>
					<Label for={fieldId} class="cursor-pointer text-sm font-normal">
						{field.placeholderText || `I agree to ${field.fieldLabel}`}
					</Label>
				</div>
			{/if}
		{:else}
			<!-- Fallback for unknown field types -->
			<Input
				id={fieldId}
				type="text"
				{value}
				placeholder={field.placeholderText}
				oninput={handleInputChange}
				class={hasErrors ? 'border-red-500 focus-visible:ring-red-500' : ''}
				aria-invalid={hasErrors}
				aria-describedby={hasErrors ? `${fieldId}-error` : undefined}
			/>
		{/if}
	</div>

	<!-- Field Errors -->
	{#if hasErrors}
		<div id="{fieldId}-error" class="space-y-1">
			{#each errors as error}
				<div class="flex items-center gap-2 text-sm text-red-600">
					<AlertCircle class="h-4 w-4 flex-shrink-0" />
					<span>{error}</span>
				</div>
			{/each}
		</div>
	{/if}
</div>

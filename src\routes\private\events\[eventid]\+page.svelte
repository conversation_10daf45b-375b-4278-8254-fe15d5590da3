<script lang="ts">
	import { Calendar } from 'lucide-svelte';

	// You can replace this with your actual data fetching

	export let data;

	// console.log('Data: ', data);

	let totalRegistrations = data.totalRegistrations?.length ?? 0;
	let totalCheckedIn = data.eventCheckInCount?.length ?? 0;
	let totalCheckedOut = data.eventCheckOutCount?.length ?? 0;
	let daysUntilEvent = '--';
	let capacityFilled = '0%';
</script>

<div class=" bg-gray-50">
	<main class="flex-1 p-1">
		<!-- Quick Stats -->
		<div class="mt-2">
			<div class="rounded-lg border bg-card text-card-foreground shadow-sm">
				<div class="p-6">
					<div class="flex items-center gap-4">
						<Calendar class="h-5 w-5 text-gray-500" />
						<h2 class="text-lg font-semibold">Quick Stats</h2>
					</div>
					<div class="mt-4 grid gap-4 md:grid-cols-3">
						<div class="rounded-lg bg-gray-50 p-4">
							<div class="text-sm text-gray-500">Total Registrations</div>
							<div class="mt-1 text-2xl font-semibold">{totalRegistrations}</div>
						</div>
						<div class="rounded-lg bg-gray-50 p-4">
							<div class="text-sm text-gray-500">Total Checked-In</div>
							<div class="mt-1 text-2xl font-semibold">{totalCheckedIn}</div>
						</div>
						<div class="rounded-lg bg-gray-50 p-4">
							<div class="text-sm text-gray-500">Total Checked-Out</div>
							<div class="mt-1 text-2xl font-semibold">{totalCheckedOut}</div>
						</div>
						<div class="rounded-lg bg-gray-50 p-4">
							<div class="text-sm text-gray-500">Days Until Event</div>
							<div class="mt-1 text-2xl font-semibold">{daysUntilEvent}</div>
						</div>
						<div class="rounded-lg bg-gray-50 p-4">
							<div class="text-sm text-gray-500">Capacity Filled</div>
							<div class="mt-1 text-2xl font-semibold">{capacityFilled}</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</main>
</div>

<slot />

<script>
	import { onMount, onDestroy } from 'svelte';
	import { browser } from '$app/environment';

	import QrcodeStream from '$lib/components/qrcode-stream.svelte';
	import { BarcodeDetector } from 'barcode-detector';
	import { fetchImageBuffer } from '$lib/utils/fetchImageBuffer';

	// let isLoggedIn = false;
	// let isScanning = false;
	// let scannerConfig = null;
	// let username = '';
	// let password = '';
	// let scanResult = null;
	// let offlineScans = [];
	// let showSettings = false;
	// let decodedText = null;
	// let showForceOptions = false;

	let isLoggedIn = $state(false);
	let isScanning = $state(false);
	let scannerConfig = $state(null);
	let username = $state('');
	let password = $state('');
	let scanResult = $state(null);
	let offlineScans = $state([]);
	let showSettings = $state(false);
	let decodedText = $state(null);
	let showForceOptions = $state(false);
	// #TODO: Add option to enter qrdata and submit
	// Load offline scans from localStorage
	onMount(() => {
		if (browser) {
			// Check if BarcodeDetector is supported
			if ('BarcodeDetector' in window) {
				console.log('BarcodeDetector is supported!');
				// Check supported formats
				BarcodeDetector.getSupportedFormats()
					.then((formats) => console.log('Supported formats:', formats))
					.catch((err) => console.error('Error getting supported formats:', err));
			} else {
				console.warn('BarcodeDetector is NOT supported in this browser');
				alert('Your browser may not support barcode detection. Please try a different browser.');
			}

			// Rest of your existing onMount code...
			const storedScans = localStorage.getItem('offlineScans');
			if (storedScans) {
				offlineScans = JSON.parse(storedScans);
			}

			// Check if already logged in
			const storedConfig = localStorage.getItem('scannerConfig');
			if (storedConfig) {
				scannerConfig = JSON.parse(storedConfig);
				isLoggedIn = true;
			}
		}

		console.log('Scan Data: ');
	});

	async function login() {
		try {
			const response = await fetch('/api/scanner/login', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ username, password })
			});

			if (!response.ok) {
				throw new Error('Login failed');
			}

			const data = await response.json();
			scannerConfig = data.scanner;

			// Store scanner config in localStorage
			localStorage.setItem('scannerConfig', JSON.stringify(scannerConfig));
			isLoggedIn = true;
		} catch (error) {
			console.error('Login error:', error);
			alert('Login failed. Please check your credentials.');
		}
	}

	function logout() {
		if (confirm('Are you sure you want to log out?')) {
			isLoggedIn = false;
			scannerConfig = null;
			localStorage.removeItem('scannerConfig');
			stopScanner();
		}
	}

	function startScanner() {
		console.log('Starting scanner...');
		isScanning = true;
	}

	function stopScanner() {
		isScanning = false;
	}

	function onDetect(detectedCodes) {
		console.log('onDetect called with:', detectedCodes);
		if (detectedCodes && detectedCodes.length > 0) {
			// Process the first detected barcode
			console.log('QR code detected:', detectedCodes[0].rawValue);
			// alert('Barcode detected: ' + detectedCodes[0].rawValue);
			decodedText = detectedCodes[0].rawValue;
			onScanSuccess(decodedText);
			stopScanner();
		}
	}

	function onScanError(error) {
		console.error('Scanning error:', error);
		alert(`Failed to start scanner: ${error.message}`);
	}

	async function onScanSuccess(decodedText) {
		// Stop scanner after successful scan
		stopScanner();

		// Process the scan
		const timestamp = new Date().toISOString();
		const scanType = getScanType();

		// Create scan record
		const scanRecord = {
			qr_data: decodedText,
			timestamp,
			scanner_id: scannerConfig.id,
			scanner_name: scannerConfig.name,
			gate: scannerConfig.gate,
			scan_type: scanType,
			processed: false,
			status: 'pending' // Add status field
		};

		// Try to send to server
		try {
			// First, check for previous scans of this QR code
			const prevScanResponse = await fetch(
				`/api/scanner/previous-scans?qr_data=${encodeURIComponent(decodedText)}`,
				{
					method: 'GET',
					headers: {
						'Content-Type': 'application/json'
					}
				}
			);

			const prevScanResult = await prevScanResponse.json();

			// Now record the current scan
			const response = await fetch('/api/scanner/record-scan', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(scanRecord)
			});

			const result = await response.json();

			if (!response.ok) {
				// Handle validation errors but still record the scan
				if (result.validation_failed) {
					scanRecord.status = 'invalid'; // Mark as invalid
					console.log('invalid QR Call API getting Called.');
					// Store the invalid scan
					try {
						await fetch('/api/scanner/record-invalid-scan', {
							method: 'POST',
							headers: {
								'Content-Type': 'application/json'
							},
							body: JSON.stringify(scanRecord)
						});
					} catch (invalidScanError) {
						console.error('Error recording invalid scan:', invalidScanError);
					}

					scanResult = {
						success: false,
						message: result.error || 'Access validation failed',
						data: result.user_data || null,
						previousScans: prevScanResult.data || [],
						validation_failed: true
					};
					return;
				}
				throw new Error('Failed to record scan');
			}

			scanResult = {
				success: true,
				message: `${scanType} recorded successfully`,
				data: result.data,
				previousScans: prevScanResult.data || []
			};
		} catch (error) {
			console.error('Error recording scan:', error);

			// Store offline
			scanRecord.processed = false;
			scanRecord.status = 'offline'; // Mark as offline
			offlineScans.push(scanRecord);
			localStorage.setItem('offlineScans', JSON.stringify(offlineScans));

			scanResult = {
				success: true,
				message: `${scanType} stored offline (will sync when online)`,
				offline: true,
				previousScans: []
			};
		}
	}

	function getScanType() {
		// Determine scan type based on scanner configuration
		if (scannerConfig.mode === 'check-in') {
			return 'check-in';
		} else if (scannerConfig.mode === 'check-out') {
			return 'check-out';
		} else if (scannerConfig.mode === 'auto') {
			// Auto mode would need to check the last scan for this badge
			// For simplicity, we'll default to check-in for now
			return 'auto';
		} else {
			// Manual mode - would need UI selection
			return 'manual';
		}
	}

	async function manualScanType(type) {
		if (!scanResult || !scanResult.success) return; // Don't process if scan was invalid
		showForceOptions = false;
		const updatedScanRecord = {
			scanner_id: scannerConfig.id,
			qr_data: decodedText,
			scan_type: type
		};

		console.log('Updated scan record:', updatedScanRecord);

		try {
			const response = await fetch('/api/scanner/update-scan', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(updatedScanRecord)
			});

			if (!response.ok) {
				throw new Error('Failed to update scan');
			}

			scanResult.message = `${type} recorded successfully`;

			// Add a short delay to show the success message before resetting
			setTimeout(() => {
				resetScan();
			}, 1500);
		} catch (error) {
			console.error('Error updating scan:', error);
			alert('Failed to update scan type. Please try again.');
		}
	}

	async function syncOfflineScans() {
		if (offlineScans.length === 0) {
			alert('No offline scans to sync');
			return;
		}

		try {
			const response = await fetch('/api/scanner/sync-offline', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ scans: offlineScans })
			});

			if (!response.ok) {
				throw new Error('Failed to sync offline scans');
			}

			// Clear offline scans
			offlineScans = [];
			localStorage.setItem('offlineScans', JSON.stringify(offlineScans));

			alert('Offline scans synced successfully');
		} catch (error) {
			console.error('Error syncing offline scans:', error);
			alert('Failed to sync offline scans. Please try again when online.');
		}
	}

	function resetScan() {
		scanResult = null;
		decodedText = null;
	}

	onDestroy(() => {
		stopScanner();
	});
</script>

<svelte:head>
	<title>QR Scanner</title>
</svelte:head>

<div class="flex min-h-screen flex-col bg-gray-100">
	<header class="bg-blue-600 p-4 text-white shadow-md">
		<div class="container mx-auto flex items-center justify-between">
			<h1 class="text-xl font-bold">QR Scanner</h1>
			{#if isLoggedIn}
				<div class="flex items-center space-x-2">
					<span>{scannerConfig.name}</span>
					<button
						class="rounded bg-blue-700 px-3 py-1 text-sm hover:bg-blue-800"
						onclick={() => (showSettings = !showSettings)}
					>
						⚙️
					</button>
				</div>
			{/if}
		</div>
	</header>

	<main class="container mx-auto flex-1 p-4">
		{#if !isLoggedIn}
			<div class="mx-auto mt-10 max-w-md rounded-lg bg-white p-6 shadow-md">
				<h2 class="mb-6 text-center text-2xl font-bold">Scanner Login</h2>
				<div class="space-y-4">
					<div>
						<label for="username" class="mb-1 block text-sm font-medium text-gray-700"
							>Username</label
						>
						<input
							type="text"
							id="username"
							bind:value={username}
							class="w-full rounded border p-2"
							placeholder="Enter scanner username"
						/>
					</div>
					<div>
						<label for="password" class="mb-1 block text-sm font-medium text-gray-700"
							>Password</label
						>
						<input
							type="password"
							id="password"
							bind:value={password}
							class="w-full rounded border p-2"
							placeholder="Enter password"
						/>
					</div>
					<button
						onclick={login}
						class="w-full rounded bg-blue-600 px-4 py-2 font-bold text-white hover:bg-blue-700"
					>
						Login
					</button>
				</div>
			</div>
		{:else if showSettings}
			<div class="mx-auto mt-4 max-w-md rounded-lg bg-white p-6 shadow-md">
				<h2 class="mb-4 text-xl font-bold">Scanner Settings</h2>
				<div class="space-y-4">
					<div>
						<p><strong>Name:</strong> {scannerConfig.name}</p>
						<p><strong>Location:</strong> {scannerConfig.location}</p>
						<p><strong>Gate:</strong> {scannerConfig.gate}</p>
						<p><strong>Mode:</strong> {scannerConfig.mode}</p>
					</div>

					<div class="border-t pt-4">
						<h3 class="mb-2 font-medium">Offline Scans</h3>
						<p>You have {offlineScans.length} offline scans.</p>
						{#if offlineScans.length > 0}
							<button
								onclick={syncOfflineScans}
								class="mt-2 rounded bg-green-600 px-4 py-2 font-bold text-white hover:bg-green-700"
							>
								Sync Offline Scans
							</button>
						{/if}
					</div>

					<div class="flex space-x-2 border-t pt-4">
						<button
							onclick={() => (showSettings = false)}
							class="rounded bg-gray-500 px-4 py-2 font-bold text-white hover:bg-gray-600"
						>
							Back
						</button>
						<button
							onclick={logout}
							class="rounded bg-red-600 px-4 py-2 font-bold text-white hover:bg-red-700"
						>
							Logout
						</button>
					</div>
				</div>
			</div>
		{:else if scanResult}
			<div>
				<h2 class="mb-4 text-xl font-bold">Scan Result</h2>
				<div class="space-y-4">
					<div class={scanResult.success ? 'text-green-600' : 'text-red-600'}>
						<p class="text-lg font-medium">{scanResult.message}</p>
					</div>

					{#if scanResult.data}
						<div
							class="mx-auto mt-4 max-w-md rounded-lg p-6 shadow-md {scanResult.success
								? 'border-2 border-green-300 bg-green-500'
								: scanResult.validation_failed
									? 'border-2 border-yellow-300 bg-yellow-500'
									: 'border-2 border-red-300 bg-red-500'}"
						>
							<div class="rounded bg-gray-100 p-4">
								<h3 class="mb-2 font-medium">Badge Information</h3>
								<!-- <img
									src={scanResult.data.converted_url}
									alt="Badge Profile"
									class="w-32 h-32 object-cover mb-2"
								/> -->
								<div class="mb-2 h-32 w-32">
									{#await fetchImageBuffer(scanResult.data.converted_url) then imageBuffer}
										{#if imageBuffer}
											<img
												src={URL.createObjectURL(new Blob([imageBuffer]))}
												alt="Badge Profile"
												class="h-full w-full object-cover"
											/>
										{:else}
											<div class="flex h-full w-full items-center justify-center bg-gray-300">
												<span>No image</span>
											</div>
										{/if}
									{:catch error}
										<div class="flex h-full w-full items-center justify-center bg-gray-300">
											<span>Failed to load image</span>
										</div>
									{/await}
								</div>
								<p>
									<strong>Name:</strong>
									{scanResult.data.first_name || 'N/A'}
									{scanResult.data.last_name || 'N/A'}
								</p>
								<p><strong>Category:</strong> {scanResult.data.category || 'N/A'}</p>
								<p><strong>Badge#:</strong> {scanResult.data.badge_id || 'N/A'}</p>

								<!-- <p><strong>Organization:</strong> {scanResult.data.organization || 'N/A'}</p> -->
							</div>
						</div>
					{/if}
					<!-- #TODO: Conditional rendering for when sure not found -->
					<!-- Only show scan type options if scan was successful -->
					{#if scannerConfig.mode === 'manual' && !scanResult.offline && scanResult.success}
						<div class="border-t pt-4">
							<h3 class="mb-2 text-lg font-medium">Select Scan Type</h3>
							<div class="flex items-center justify-center space-x-2">
								<button
									onclick={() => manualScanType('check-in')}
									class="rounded bg-green-600 px-4 py-2 text-2xl font-bold text-white hover:bg-green-700 {scanResult.previousScans &&
									scanResult.previousScans.length > 0 &&
									(scanResult.previousScans[0].scan_type === 'check-in' ||
										scanResult.previousScans[0].scan_type === 'force-check-in')
										? 'cursor-not-allowed opacity-50'
										: ''}"
									disabled={scanResult.previousScans &&
										scanResult.previousScans.length > 0 &&
										(scanResult.previousScans[0].scan_type === 'check-in' ||
											scanResult.previousScans[0].scan_type === 'force-check-in')}
								>
									Check In
								</button>
								<button
									onclick={() => manualScanType('check-out')}
									class="rounded bg-orange-600 px-4 py-2 text-2xl font-bold text-white hover:bg-orange-700 {scanResult.previousScans &&
									scanResult.previousScans.length > 0 &&
									(scanResult.previousScans[0].scan_type === 'check-out' ||
										scanResult.previousScans[0].scan_type === 'force-check-out')
										? 'cursor-not-allowed opacity-50'
										: ''}"
									disabled={scanResult.previousScans &&
										scanResult.previousScans.length > 0 &&
										(scanResult.previousScans[0].scan_type === 'check-out' ||
											scanResult.previousScans[0].scan_type === 'force-check-out')}
								>
									Check Out
								</button>
								<div class="relative">
									<button
										onclick={() => (showForceOptions = !showForceOptions)}
										class="rounded bg-gray-500 px-3 py-2 text-2xl font-bold text-white hover:bg-gray-600"
									>
										⋮
									</button>
									{#if showForceOptions}
										<div class="absolute right-0 z-10 mt-2 w-48 rounded-md bg-white shadow-lg">
											<div class="py-1">
												<button
													onclick={() => manualScanType('force-check-in')}
													class="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
												>
													Force Check In
												</button>
												<button
													onclick={() => manualScanType('force-check-out')}
													class="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
												>
													Force Check Out
												</button>
											</div>
										</div>
									{/if}
								</div>
							</div>
						</div>
					{/if}

					<!-- Only show previous scans section if scan was successful -->
					{#if scanResult && scanResult.success && scanResult.previousScans && scanResult.previousScans.length > 0}
						<div class="mt-4 rounded-lg bg-gray-100 p-4">
							<h3 class="mb-2 text-lg font-semibold">Previous Scans</h3>
							<div class="space-y-2">
								{#each scanResult.previousScans as scan, i}
									{#if i === 0}
										<div class="rounded border border-blue-300 bg-blue-100 p-2">
											<p class="font-medium">Last scan: {scan.scan_type} at {scan.gate}</p>
											<p class="text-sm text-gray-600">
												{new Date(scan.timestamp).toLocaleString()}
											</p>
										</div>
									{:else}
										<div class="rounded border border-gray-200 bg-gray-50 p-2">
											<p>{scan.scan_type} at {scan.gate}</p>
											<p class="text-sm text-gray-600">
												{new Date(scan.timestamp).toLocaleString()}
											</p>
										</div>
									{/if}
								{/each}
							</div>
						</div>
					{:else if scanResult && scanResult.success && scanResult.previousScans && scanResult.previousScans.length === 0}
						<div class="mt-4 rounded-lg bg-gray-100 p-4">
							<p>No previous scans found for this QR code.</p>
						</div>
					{/if}

					<div class="flex justify-between pt-4">
						<!-- Hide the New Scan button -->
						{#if !scanResult.success}
							<button
								onclick={resetScan}
								class="rounded bg-blue-600 px-4 py-2 font-bold text-white hover:bg-blue-700"
							>
								New Scan
							</button>
						{/if}
					</div>
				</div>
			</div>
		{:else}
			<div class="mx-auto mt-4 max-w-md rounded-lg bg-white p-6 shadow-md">
				{#if isScanning}
					<div class="mb-4">
						<h2 class="mb-2 text-xl font-bold">Scanning...</h2>
						<p class="mb-4 text-sm text-gray-600">Position the QR code within the scanner area</p>
						<div class="relative h-64 w-full overflow-hidden rounded bg-black">
							<QrcodeStream
								formats={['qr_code']}
								{onDetect}
								onError={onScanError}
								paused={!isScanning}
							>
								<div
									class="absolute inset-0 m-auto h-48 w-48 border-2 border-white opacity-50"
								></div>
							</QrcodeStream>
						</div>
					</div>
					<button
						onclick={stopScanner}
						class="w-full rounded bg-red-600 px-4 py-2 font-bold text-white hover:bg-red-700"
					>
						Cancel
					</button>
				{:else}
					<h2 class="mb-4 text-xl font-bold">Ready to Scan</h2>
					<div class="space-y-4">
						<p>Scanner: <strong>{scannerConfig.name}</strong></p>
						<p>Location: <strong>{scannerConfig.location}</strong></p>
						<p>Gate: <strong>{scannerConfig.gate}</strong></p>
						<p>Mode: <strong>{scannerConfig.mode}</strong></p>

						<div class="flex space-x-2 pt-4">
							<button
								onclick={startScanner}
								class="flex-1 rounded bg-blue-600 px-4 py-3 font-bold text-white hover:bg-blue-700"
							>
								Start Scanner
							</button>
							<!-- <button
								on:click={() => (showSettings = true)}
								class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-4 rounded"
							>
								Settings
							</button> -->
						</div>

						{#if offlineScans.length > 0}
							<div class="border-t pt-4">
								<p class="text-amber-600">
									You have {offlineScans.length} offline scans that need to be synced.
								</p>
								<button
									onclick={syncOfflineScans}
									class="mt-2 rounded bg-amber-600 px-4 py-2 font-bold text-white hover:bg-amber-700"
								>
									Sync Now
								</button>
							</div>
						{/if}
					</div>
				{/if}
			</div>
		{/if}
	</main>

	<footer class="mt-auto bg-gray-800 p-4 text-white">
		<div class="container mx-auto text-center text-sm">
			<p>QR Scanner App v1.0</p>
		</div>
	</footer>
</div>

<!-- <pre>{JSON.stringify(scanResult, null, 2)}</pre> -->

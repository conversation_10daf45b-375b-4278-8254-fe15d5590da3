import { redirect, type Actions, error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals: { supabase, safeGetSession } }) => {
	const { session } = await safeGetSession();
	if (!session) {
		redirect(303, '/login');
	}
	// console.log('Session: ', session.user.id);

	// const { data: organizerId } = await supabase
	// 	.from('organizers')
	// 	.select(`id`)
	// 	.eq('user_id', session.user.id)
	// 	.single();

	// console.log('Organizer ID: ', organizerId?.id);

	const { data: eventsData } = await supabase
		.from('events')
		.select()
	// .eq('organizer_id', organizerId?.id);

	// console.log('Events Data: ', eventsData);

	return { session, eventsData };
};

export const actions: Actions = {
	togglePublish: async ({ request, locals: { supabase, safeGetSession } }) => {
		const { session } = await safeGetSession();
		if (!session) {
			redirect(303, '/login');
		}

		const formData = await request.formData();
		const eventId = formData.get('eventId') as string;
		const currentStatus = formData.get('currentStatus') === 'true';

		if (!eventId) {
			error(400, { message: 'Event ID is required' });
		}

		// Toggle the publish status
		const newStatus = !currentStatus;

		const { error: updateError } = await supabase
			.from('events')
			.update({ is_published: newStatus })
			.eq('id', eventId);

		if (updateError) {
			console.error('Error updating event publish status:', updateError);
			error(500, { message: 'Failed to update event status' });
		}

		// Return success - the page will automatically refresh
		return { success: true };
	},


	delete: async ({ request, locals: { supabase, safeGetSession } }) => {
		const { session } = await safeGetSession();
		if (!session) {
			return;
		}
		const formData = await request.formData();
		console.log('Form Data: ', formData);
		const eventId = formData.get('eventId') as string;
		console.log('Delete Action Celled with Event ID: ', eventId);
	}
};

// export const actions: Actions = {
// 	delete: async ({ request, locals: { supabase, safeGetSession } }) => {
// 		const { session } = await safeGetSession();
// 		if (!session) {
// 			return;
// 		}
// 		const formData = await request.formData();
// 		console.log('Form Data: ', formData);
// 		const eventId = formData.get('eventId') as string;
// 		console.log('Delete Action Celled with Event ID: ', eventId);
// 	}
// };

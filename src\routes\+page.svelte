<script lang="ts">
	import { Button } from '$lib/components/ui/button';

	import { Input } from '$lib/components/ui/input';
	import * as Select from '$lib/components/ui/select/index.js';
	// import { Calendar, Filter, MapPin, Search } from 'lucide-svelte';
	import Filter from 'lucide-svelte/icons/filter';
	import Search from 'lucide-svelte/icons/search';
	import NewEventCard from '$lib/components/NewEventCard.svelte';
	import Footer from '$lib/components/Footer.svelte';
	import { page } from '$app/stores';
	import eventImage from '$lib/images/logo.png';

	export let data;
	$: ({ events } = data);

	// Define your page metadata here
	const title = 'eHaris Events';
	const description = `Quickly find and register for events near you.`;
	const image = eventImage;
	const siteName = 'eventsdemo.eharis.com';
</script>

<svelte:head>
	<title>{title}</title>
	<meta name="description" content={description} />

	<!-- Open Graph / Facebook -->
	<meta property="og:type" content="website" />
	<meta property="og:url" content={$page.url.href} />
	<meta property="og:title" content={title} />
	<meta property="og:description" content={description} />
	<meta property="og:image" content={eventImage} />

	<!-- Twitter -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:url" content={$page.url.href} />
	<meta name="twitter:title" content={title} />
	<meta name="twitter:description" content={description} />
	<meta name="twitter:image" content={eventImage} />

	<!-- WhatsApp -->
	<meta property="og:site_name" content={siteName} />
	<meta property="og:locale" content="en_US" />
	<meta property="og:image:width" content="1200" />
	<meta property="og:image:height" content="630" />
</svelte:head>

<div class="min-h-screen bg-gray-50/50">
	<main class="container py-8">
		<div class="flex flex-col gap-8">
			<div class="flex flex-col gap-4">
				<h1 class="text-2xl font-bold tracking-tight lg:text-3xl">Upcoming Events</h1>
				<div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
					<div class="flex w-full max-w-sm items-center space-x-2">
						<Input placeholder="Search events..." type="search" />
						<Button size="icon" variant="ghost">
							<Search class="h-4 w-4" />
							<span class="sr-only">Search</span>
						</Button>
					</div>
					<div class="flex items-center gap-2">
						<Button variant="outline" size="sm">
							<Filter class="mr-2 h-4 w-4" />
							Filter
						</Button>
						<Select.Root type="single" value="all">
							<Select.Trigger class="w-[140px]">All Events</Select.Trigger>
							<Select.Content>
								<Select.Item value="all">All Events</Select.Item>
								<Select.Item value="conference">Conferences</Select.Item>
								<Select.Item value="concert">Concerts</Select.Item>
								<Select.Item value="sports">Sports</Select.Item>
								<Select.Item value="expo">Exhibitions</Select.Item>
							</Select.Content>
						</Select.Root>
					</div>
				</div>
			</div>
			<div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
				{#each events as event}
					<NewEventCard props={event} />
				{/each}
			</div>
		</div>
	</main>
</div>
<Footer />

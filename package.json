{"name": "org-events-template", "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "devDependencies": {"@internationalized/date": "^3.5.6", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/adapter-node": "^5.2.9", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^4.0.0", "@types/eslint": "^9.6.0", "autoprefixer": "^10.4.20", "bits-ui": "^1.8.0", "clsx": "^2.1.1", "eslint": "^9.7.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.36.0", "globals": "^15.0.0", "lucide-svelte": "^0.456.0", "prettier": "^3.3.2", "prettier-plugin-svelte": "^3.2.6", "prettier-plugin-tailwindcss": "^0.6.5", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "svelte-hot-french-toast": "^1.0.0", "svelte-sonner": "^0.3.28", "svelte-turnstile": "^0.9.0", "sveltekit-superforms": "^2.20.1", "tailwind-merge": "^2.5.4", "tailwind-variants": "^0.3.0", "tailwindcss": "^3.4.9", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "typescript-eslint": "^8.0.0", "vite": "^5.0.3", "zod": "^3.23.8"}, "dependencies": {"@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.46.1", "@tanstack/table-core": "^8.20.5", "@types/pdfkit": "^0.13.8", "barcode-detector": "^2.3.1", "class-variance-authority": "^0.7.1", "html2canvas": "^1.4.1", "marked": "^15.0.11", "papaparse": "^5.4.1", "pdfjs-dist": "^4.9.155", "pdfkit": "^0.15.2", "posthog-js": "^1.255.1", "qrcode": "^1.5.4", "webrtc-adapter": "^9.0.1", "zeptomail": "^6.1.0"}}
import { error } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals: { supabase } }) => {
    // First check if social share data exists
    const { data: socialShare, error: fetchError } = await supabase
        .from('social_shares')
        .select('share_image_url, share_text')
        .eq('event_id', params.eventid)
        .single();

    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is the "not found" error code
        console.error('Error fetching social share settings:', fetchError);
        // throw error(500, 'Failed to load social share settings');
    }

    return {
        shareImageUrl: socialShare?.share_image_url || '',
        shareText: socialShare?.share_text || '',
        exists: !!socialShare
    };
};

export const actions: Actions = {
    updateSocialShare: async ({ request, params, locals: { supabase } }) => {
        const formData = await request.formData();
        const shareText = formData.get('shareText') as string;
        const shareImage = formData.get('shareImage') as File;

        let shareImageUrl = null;

        // Upload image if provided
        if (shareImage && shareImage.size > 0) {
            const fileExt = shareImage.name.split('.').pop();
            const fileName = `${params.eventid}_share_${Date.now()}.${fileExt}`;

            const { error: uploadError } = await supabase.storage
                .from('event-share-images')
                .upload(fileName, shareImage);

            if (uploadError) {
                console.error('Error uploading image:', uploadError);
                throw error(500, 'Failed to upload image');
            }

            const { data: { publicUrl } } = supabase.storage
                .from('event-share-images')
                .getPublicUrl(fileName);

            shareImageUrl = publicUrl;
        }

        // Check if record exists
        const { data: existingData, error: checkError } = await supabase
            .from('social_shares')
            .select('id')
            .eq('event_id', params.eventid)
            .single();

        if (checkError && checkError.code !== 'PGRST116') {
            console.error('Error checking existing record:', checkError);
            throw error(500, 'Failed to check existing record');
        }

        const updateData = {
            share_text: shareText,
            event_id: params.eventid,
            ...(shareImageUrl && { share_image_url: shareImageUrl })
        };

        let dbError;
        if (existingData) {
            // Update existing record
            const { error: updateError } = await supabase
                .from('social_shares')
                .update(updateData)
                .eq('event_id', params.eventid);
            dbError = updateError;
        } else {
            // Insert new record
            const { error: insertError } = await supabase
                .from('social_shares')
                .insert([updateData]);
            dbError = insertError;
        }

        if (dbError) {
            console.error('Error updating/inserting social share settings:', dbError);
            throw error(500, 'Failed to save social share settings');
        }

        return { success: true };
    }
};


# Testing Guide for Flexible Forms

This guide helps you test the flexible forms feature implementation.

## Quick Setup

### 1. Database Setup
First, run the database migration:

```sql
-- Connect to your Supabase database and run:
\i database/migrations/001_create_flexible_forms_tables.sql
```

### 2. Test the Implementation

#### Option A: Using the Test API
```bash
# Test field initialization for an existing event
curl -X POST http://localhost:5173/api/test-form-fields \
  -H "Content-Type: application/json" \
  -d '{
    "action": "initialize",
    "eventId": "your-event-id-here"
  }'

# Test getting fields
curl -X POST http://localhost:5173/api/test-form-fields \
  -H "Content-Type: application/json" \
  -d '{
    "action": "get-fields", 
    "eventId": "your-event-id-here"
  }'
```

#### Option B: Using the Admin Interface
1. Navigate to any event dashboard
2. Click "Form Builder" 
3. Toggle standard fields on/off
4. Create a custom field
5. Preview the form

### 3. Test Registration Form

#### Dynamic Form (New)
- If form fields are configured, the dynamic form will be used automatically
- Visit any event page to see the dynamic form in action

#### Legacy Form (Fallback)
- If no form fields are configured, the original hardcoded form is used
- This ensures backward compatibility

## Testing Scenarios

### Scenario 1: New Event with Dynamic Form
1. Create a new event
2. Go to Form Builder
3. Configure fields (toggle standard fields, add custom fields)
4. Visit the event registration page
5. Verify dynamic form is displayed
6. Test form submission

### Scenario 2: Existing Event (Legacy)
1. Visit an existing event that hasn't been configured
2. Verify the original form is displayed
3. Test that registration still works

### Scenario 3: Form Field Management
1. Go to Form Builder for any event
2. Test toggling standard fields
3. Test creating custom fields with different types:
   - Text input
   - Email input
   - Phone input
   - Dropdown (select)
   - Checkboxes
   - Text area
4. Test field validation rules
5. Test field reordering

### Scenario 4: Form Validation
1. Create fields with validation rules
2. Test client-side validation
3. Test server-side validation
4. Test required field validation

## Expected Behavior

### Form Builder Interface
- ✅ Standard fields can be toggled on/off
- ✅ Custom fields can be created with various types
- ✅ Field validation rules can be configured
- ✅ Form preview works correctly
- ✅ Changes are saved and persist

### Registration Form
- ✅ Dynamic form renders based on configuration
- ✅ All field types work correctly
- ✅ Validation works in real-time
- ✅ Form submission saves data correctly
- ✅ Fallback to legacy form when no fields configured

### Data Storage
- ✅ Form responses are saved in flexible structure
- ✅ Data can be retrieved for admin viewing
- ✅ Existing guest data remains intact

## Troubleshooting

### Common Issues

#### 1. "Form fields not loading"
- Check if the event exists
- Verify database migration was run
- Check browser console for API errors

#### 2. "Dynamic form not showing"
- Ensure form fields are configured for the event
- Check that fields are enabled
- Verify the event ID is correct

#### 3. "Form submission fails"
- Check validation rules
- Verify required fields are filled
- Check server logs for errors

#### 4. "Admin interface not accessible"
- Ensure user is logged in as admin
- Check admin permissions in database
- Verify event ownership

### Debug Steps

1. **Check Database**:
   ```sql
   -- Check if form fields exist
   SELECT * FROM form_fields WHERE event_id = 'your-event-id';
   
   -- Check form responses
   SELECT * FROM form_responses WHERE event_id = 'your-event-id';
   ```

2. **Check API Endpoints**:
   ```bash
   # Test field retrieval
   curl http://localhost:5173/api/events/your-event-id/form-fields
   ```

3. **Check Browser Console**:
   - Look for JavaScript errors
   - Check network requests
   - Verify API responses

## Performance Testing

### Load Testing
- Test with multiple concurrent form submissions
- Test with large numbers of form fields
- Test with complex validation rules

### Database Performance
- Monitor query performance
- Check index usage
- Test with large datasets

## Security Testing

### Access Control
- Verify admin-only access to form builder
- Test unauthorized API access
- Check data isolation between events

### Input Validation
- Test malicious input in form fields
- Test SQL injection attempts
- Test XSS prevention

## Integration Testing

### Backward Compatibility
- Test existing events continue to work
- Verify legacy form functionality
- Test data migration scenarios

### API Integration
- Test all CRUD operations on form fields
- Test form response submission
- Test error handling

## Success Criteria

The implementation is successful when:
- ✅ All test scenarios pass
- ✅ No existing functionality is broken
- ✅ Performance is acceptable
- ✅ Security requirements are met
- ✅ User experience is intuitive

## Next Steps After Testing

1. **Production Deployment**:
   - Run database migrations
   - Deploy application code
   - Monitor for issues

2. **User Training**:
   - Train admin users on form builder
   - Document common use cases
   - Provide support materials

3. **Monitoring**:
   - Set up error monitoring
   - Track form submission rates
   - Monitor performance metrics

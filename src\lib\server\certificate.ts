// src/lib/server/certificate.js
import PDFDocument from 'pdfkit';
// import { readFileSync } from 'fs';
import { join } from 'path';

export async function generateCertificatePDF(certificateData) {
    console.log('Cert Data received in Generate Certificate PDF Function.')
    console.log('Data:', certificateData)

    const doc = new PDFDocument({
        layout: 'landscape',
        size: 'A4',
        autoFirstPage: true
    });

    // Helper for centering text
    const centerText = (text, fontSize, y) => {
        doc.fontSize(fontSize);
        const textWidth = doc.widthOfString(text);
        const pageWidth = doc.page.width;
        return (pageWidth - textWidth) / 2;
    };

    try {
        // Register Arabic font
        doc.registerFont('Arabic', join(process.cwd(), 'src', 'lib', 'fonts', 'Amiri-Regular.ttf'));
        doc.registerFont('Arabic-Bold', join(process.cwd(), 'src', 'lib', 'fonts', 'Amiri-Bold.ttf'));

        // Load and add background image
        const imagePath = join(process.cwd(), 'src', 'lib', 'images', 'certificate.png');
        doc.image(imagePath, 0, 0, {
            width: doc.page.width,
            height: doc.page.height
        });
    } catch (error) {
        console.error('Error loading assets:', error);
        // Continue without background if image fails to load
    }



    // Guest Name (Arabic)
    doc.font('Arabic-Bold')
        .fontSize(40)
        .text(certificateData.recipientName,
            centerText(certificateData.recipientName, 40, 240),
            220, {
            features: ['rtla', 'arab'] // Enable Arabic text features
        });


    // Workshop Name (Arabic)
    doc.font('Arabic-Bold')
        .fontSize(30)
        .text(certificateData.courseName,
            centerText(certificateData.courseName, 30, 320), // 
            320, {
            features: ['rtla', 'arab']
        });


    // Date
    if (certificateData.completionDate) {
        doc.font('Helvetica')
            .fontSize(20)
            .text(`${new Date(certificateData.completionDate).toLocaleDateString()}`,
                centerText(`${new Date(certificateData.completionDate).toLocaleDateString()}`, 20, 380), // left and right
                390); // first is Font size, second is left and right and third is up and down
    }

    // Date of the Certificate issued
    doc.font('Helvetica')
        .fontSize(12)
        .text(`${new Date().toLocaleDateString()}`, 225, 490);

    // Certificate ID and Issuer
    // doc.font('Helvetica')
    //     .fontSize(12)
    //     .text(`Certificate ID: ${certificateData.certificateId}`, 50, 500);



    // Guest Name
    // doc.font('Helvetica-Bold')
    //     .fontSize(40)
    //     .text(certificateData.recipientName,
    //         centerText(certificateData.recipientName, 40, 220),
    //         240);


    // Wrokshop Name
    // doc.font('Helvetica-Bold')
    //     .fontSize(30)
    //     .text(certificateData.courseName,
    //         centerText(certificateData.courseName, 30, 320),
    //         320);



    // Date (Arabic)
    // if (certificateData.completionDate) {
    //     const date = new Date(certificateData.completionDate);
    //     // Format date in Arabic
    //     const arabicDate = new Intl.DateTimeFormat('ar-SA', {
    //         year: 'numeric',
    //         month: 'long',
    //         day: 'numeric'
    //     }).format(date);

    //     doc.font('Arabic')
    //         .fontSize(20)
    //         .text(`تاريخ الإصدار: ${arabicDate}`,
    //             centerText(`تاريخ الإصدار: ${arabicDate}`, 20, 380),
    //             380, {
    //             features: ['rtla', 'arab']
    //         });
    // }


    // Certificate ID (Mixed Arabic/English)
    // doc.font('Arabic')
    //     .fontSize(12)
    //     .text(`رقم الشهادة: ${certificateData.certificateId}`, 50, 500, {
    //         features: ['rtla', 'arab']
    //     });

    console.log()

    // Return the PDF as a Buffer
    return new Promise((resolve, reject) => {
        const chunks = [];
        doc.on('data', chunk => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));
        doc.on('error', reject);
        doc.end();
    });
}
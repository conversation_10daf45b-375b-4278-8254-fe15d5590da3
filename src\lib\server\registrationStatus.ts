import type { SupabaseClient } from '@supabase/supabase-js';

/**
 * Check if registration should be automatically closed based on event start time
 */
export async function checkAndUpdateRegistrationStatus(
	supabase: SupabaseClient,
	eventId: string
): Promise<{ registrationOpen: boolean; reason?: string }> {
	// Get event data
	const { data: eventData, error: eventError } = await supabase
		.from('events')
		.select('id, start_date, start_time, registration_open, is_published')
		.eq('id', eventId)
		.single();

	if (eventError || !eventData) {
		return { registrationOpen: false, reason: 'Event not found' };
	}

	// Check if event is published
	if (!eventData.is_published) {
		return { registrationOpen: false, reason: 'Event is not published' };
	}

	// Check if registration is manually closed
	if (!eventData.registration_open) {
		return { registrationOpen: false, reason: 'Registration is closed' };
	}

	// Check if event has already started
	const eventStartDateTime = new Date(`${eventData.start_date}T${eventData.start_time}`);
	const now = new Date();

	if (now >= eventStartDateTime) {
		// Event has started, automatically close registration
		const { error: updateError } = await supabase
			.from('events')
			.update({ registration_open: false })
			.eq('id', eventId);

		if (updateError) {
			console.error('Error auto-closing registration:', updateError);
		}

		return { registrationOpen: false, reason: 'Registration closed - event has started' };
	}

	return { registrationOpen: true };
}

/**
 * Check if an event's registration is open (including automatic closure check)
 */
export async function isRegistrationOpen(
	supabase: SupabaseClient,
	eventId: string
): Promise<boolean> {
	const status = await checkAndUpdateRegistrationStatus(supabase, eventId);
	return status.registrationOpen;
}

/**
 * Get registration status with detailed information
 */
export async function getRegistrationStatus(
	supabase: SupabaseClient,
	eventId: string
): Promise<{
	isOpen: boolean;
	reason?: string;
	capacity?: {
		current: number;
		max: number | null;
		isFull: boolean;
		waitingListEnabled: boolean;
	};
}> {
	const status = await checkAndUpdateRegistrationStatus(supabase, eventId);

	if (!status.registrationOpen) {
		return {
			isOpen: false,
			reason: status.reason
		};
	}

	// Get capacity information
	const { data: eventData, error: eventError } = await supabase
		.from('events')
		.select('max_registrations, waiting_list')
		.eq('id', eventId)
		.single();

	if (eventError) {
		return {
			isOpen: false,
			reason: 'Error checking event capacity'
		};
	}

	// Count current registrations
	const { count, error: countError } = await supabase
		.from('guests')
		.select('id', { count: 'exact' })
		.eq('event_id', eventId)
		.in('status', ['registered', 'imported']);

	if (countError) {
		return {
			isOpen: false,
			reason: 'Error checking registration count'
		};
	}

	const currentCount = count || 0;
	const maxRegistrations = eventData.max_registrations;
	const isFull = maxRegistrations !== null && currentCount >= maxRegistrations;

	return {
		isOpen: true,
		capacity: {
			current: currentCount,
			max: maxRegistrations,
			isFull,
			waitingListEnabled: eventData.waiting_list || false
		}
	};
}

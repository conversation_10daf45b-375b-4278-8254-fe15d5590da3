<script lang="ts">
	import { goto } from '$app/navigation';
	import { Button } from '$lib/components/ui/button';
	import * as Table from '$lib/components/ui/table/index.js';
	import type { PageData } from './$types';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { formatDate, formatTime } from '$lib/utils/formatdatetime';
	import { enhance } from '$app/forms';

	export let data: PageData;
</script>

<header class="m-4 flex flex-row items-center justify-between">
	<h1 class=" text-4xl font-bold">All Events</h1>
	<Button href="./events/create">Create Event</Button>
</header>

<main>
	<div class="container mx-auto py-10">
		<Table.Root>
			<Table.Caption>A list of all recent events.</Table.Caption>
			<Table.Header>
				<Table.Row>
					<Table.Head>Name</Table.Head>
					<Table.Head class="text-center">Start Date & Time</Table.Head>
					<Table.Head class="text-center">Status</Table.Head>
					<Table.Head class="text-center">Actions</Table.Head>
				</Table.Row>
			</Table.Header>
			<Table.Body>
				{#each data.eventsData as event, i (i)}
					<Table.Row>
						<Table.Cell class="font-medium"
							><button onclick={() => goto(`/private/events/${event.id}`)} class="text-blue-600"
								>{event.title}</button
							></Table.Cell
						>
						<Table.Cell class="text-center"
							>{formatDate(event.start_date)} - {formatTime(event.start_time)}</Table.Cell
						>
						<Table.Cell class="text-center">
							{#if event.is_published}
								<Badge class="bg-green-400">Published</Badge>
							{:else}
								<Badge class="bg-gray-400">Draft</Badge>
							{/if}
							{#if event.is_private}
								<Badge class="ml-1 bg-blue-400">Private</Badge>
							{/if}
							{#if !event.registration_open}
								<Badge class="ml-1 bg-red-400">Closed</Badge>
							{/if}
						</Table.Cell>
						<Table.Cell class="text-center">
							<div class="flex justify-center gap-2">
								<form method="POST" action="?/togglePublish" use:enhance>
									<input type="hidden" name="eventId" value={event.id} />
									<input type="hidden" name="currentStatus" value={event.is_published} />
									{#if event.is_published}
										<Button type="submit" variant="outline" size="sm">Unpublish</Button>
									{:else}
										<Button type="submit" variant="default" size="sm">Publish</Button>
									{/if}
								</form>
								<Button
									onclick={() => goto(`/private/events/${event.id}/edit`)}
									variant="outline"
									size="sm"
								>
									Edit
								</Button>
							</div>
						</Table.Cell>
					</Table.Row>
				{/each}
			</Table.Body>
			<!-- <Table.Footer> -->
			<!-- <Table.Row>
					<Table.Cell colspan={3}>Total</Table.Cell>
					<Table.Cell class="text-right">$2,500.00</Table.Cell>
				</Table.Row> -->
			<!-- </Table.Footer> -->
		</Table.Root>
	</div>
</main>

<!-- <pre>{JSON.stringify(data.eventsData, null, 2)}</pre> -->

import posthog from 'posthog-js';
import { browser } from '$app/environment';
import { PUBLIC_POSTHOG_KEY, PUBLIC_POSTHOG_HOST } from '$env/static/public';

export interface PostHogConfig {
	apiKey: string;
	apiHost?: string;
	options?: {
		// Product Analytics
		capture_pageview?: boolean;
		capture_pageleave?: boolean;
		
		// Session Replay
		session_recording?: {
			recordCrossOriginIframes?: boolean;
			recordCanvas?: boolean;
			recordPerformance?: boolean;
			maskAllInputs?: boolean;
			maskInputOptions?: {
				password?: boolean;
				email?: boolean;
			};
		};
		
		// Error Tracking
		capture_heatmaps?: boolean;
		
		// Web Analytics
		persistence?: 'localStorage' | 'cookie' | 'memory';
		persistence_name?: string;
		cookie_expiration?: number;
		
		// Privacy & GDPR
		opt_out_capturing_by_default?: boolean;
		respect_dnt?: boolean;
		
		// Advanced Options
		loaded?: (posthog: any) => void;
		autocapture?: boolean;
		disable_session_recording?: boolean;
		debug?: boolean;
	};
}

// Default configuration with all requested features enabled
const defaultConfig: PostHogConfig = {
	apiKey: PUBLIC_POSTHOG_KEY || 'phc_kYV0tnmpapd7giA8sKKMs3YqyZ55sJMHyMlnI7Tr9zl',
	apiHost: PUBLIC_POSTHOG_HOST || 'https://app.posthog.com',
	options: {
		// Product Analytics - Enable page tracking
		capture_pageview: true,
		capture_pageleave: true,
		
		// Session Replay - Enable with privacy-conscious defaults
		session_recording: {
			recordCrossOriginIframes: true,
			recordCanvas: false, // Disabled by default for privacy
			recordPerformance: true,
			maskAllInputs: false,
			maskInputOptions: {
				password: true,
				email: true
			}
		},
		
		// Web Analytics
		persistence: 'localStorage',
		persistence_name: '__posthog',
		cookie_expiration: 365,
		
		// Error Tracking & Heatmaps
		capture_heatmaps: true,
		
		// Privacy Settings
		opt_out_capturing_by_default: false,
		respect_dnt: true,
		
		// Advanced Features
		autocapture: true,
		disable_session_recording: false,
		debug: false, // Set to true for development debugging
		
		loaded: (posthog) => {
			console.log('PostHog loaded successfully');
			// Additional setup can be done here
		}
	}
};

// Initialize PostHog with configuration
export const initializePostHog = (config: Partial<PostHogConfig> = {}) => {
	if (!browser) return;
	
	const finalConfig = { ...defaultConfig, ...config };
	
	if (!finalConfig.apiKey) {
		console.warn('PostHog API key not provided. Analytics will not be initialized.');
		return;
	}
	
	try {
		posthog.init(finalConfig.apiKey, {
			api_host: finalConfig.apiHost,
			...finalConfig.options
		});
		
		console.log('PostHog initialized with config:', {
			apiKey: finalConfig.apiKey.substring(0, 10) + '...',
			apiHost: finalConfig.apiHost,
			features: {
				productAnalytics: finalConfig.options?.capture_pageview,
				sessionReplay: !finalConfig.options?.disable_session_recording,
				errorTracking: finalConfig.options?.capture_heatmaps,
				webAnalytics: true
			}
		});
		
		return posthog;
	} catch (error) {
		console.error('Failed to initialize PostHog:', error);
	}
};

// Feature flag helpers
export const enableSessionReplay = () => {
	if (!browser || !posthog.__loaded) return;
	posthog.startSessionRecording();
};

export const disableSessionReplay = () => {
	if (!browser || !posthog.__loaded) return;
	posthog.stopSessionRecording();
};

// Privacy helpers
export const optOut = () => {
	if (!browser || !posthog.__loaded) return;
	posthog.opt_out_capturing();
};

export const optIn = () => {
	if (!browser || !posthog.__loaded) return;
	posthog.opt_in_capturing();
};

// User identification helpers
export const identifyUser = (userId: string, properties?: Record<string, any>) => {
	if (!browser || !posthog.__loaded) return;
	posthog.identify(userId, properties);
};

export const resetUser = () => {
	if (!browser || !posthog.__loaded) return;
	posthog.reset();
};

// Export the PostHog instance for direct access
export { posthog };
export default posthog;

// Server-side form field management functions

import { supabaseAdmin } from '$lib/server/db/supabaseAdmin';
import type { 
  FormField, 
  FormFieldCreate, 
  FormFieldUpdate, 
  STANDARD_FIELDS,
  ValidationRules,
  FormFieldType 
} from '$lib/types/formFields';

/**
 * Initialize standard fields for a new event
 */
export async function initializeStandardFields(eventId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabaseAdmin.rpc('initialize_standard_fields', {
      p_event_id: eventId
    });

    if (error) {
      console.error('Error initializing standard fields:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in initializeStandardFields:', error);
    return { success: false, error: 'Failed to initialize standard fields' };
  }
}

/**
 * Get all form fields for an event
 */
export async function getEventFormFields(eventId: string): Promise<{ data: <PERSON><PERSON>ield[] | null; error?: string }> {
  try {
    const { data, error } = await supabaseAdmin
      .from('form_fields')
      .select('*')
      .eq('event_id', eventId)
      .order('display_order', { ascending: true });

    if (error) {
      console.error('Error fetching form fields:', error);
      return { data: null, error: error.message };
    }

    const formFields: FormField[] = data.map(field => ({
      id: field.id,
      eventId: field.event_id,
      fieldKey: field.field_key,
      fieldLabel: field.field_label,
      fieldType: field.field_type as FormFieldType,
      fieldOptions: field.field_options,
      isRequired: field.is_required,
      isStandardField: field.is_standard_field,
      isEnabled: field.is_enabled,
      displayOrder: field.display_order,
      validationRules: field.validation_rules,
      placeholderText: field.placeholder_text,
      helpText: field.help_text,
      createdAt: field.created_at,
      updatedAt: field.updated_at
    }));

    return { data: formFields };
  } catch (error) {
    console.error('Error in getEventFormFields:', error);
    return { data: null, error: 'Failed to fetch form fields' };
  }
}

/**
 * Get enabled form fields for an event (for public registration form)
 */
export async function getEnabledFormFields(eventId: string): Promise<{ data: FormField[] | null; error?: string }> {
  try {
    const { data, error } = await supabaseAdmin.rpc('get_event_form_fields', {
      p_event_id: eventId
    });

    if (error) {
      console.error('Error fetching enabled form fields:', error);
      return { data: null, error: error.message };
    }

    const formFields: FormField[] = data.map((field: any) => ({
      id: field.id,
      eventId: eventId,
      fieldKey: field.field_key,
      fieldLabel: field.field_label,
      fieldType: field.field_type as FormFieldType,
      fieldOptions: field.field_options,
      isRequired: field.is_required,
      isStandardField: field.is_standard_field,
      isEnabled: field.is_enabled,
      displayOrder: field.display_order,
      validationRules: field.validation_rules,
      placeholderText: field.placeholder_text,
      helpText: field.help_text,
      createdAt: '',
      updatedAt: ''
    }));

    return { data: formFields };
  } catch (error) {
    console.error('Error in getEnabledFormFields:', error);
    return { data: null, error: 'Failed to fetch enabled form fields' };
  }
}

/**
 * Create a new custom form field
 */
export async function createFormField(fieldData: FormFieldCreate): Promise<{ data: FormField | null; error?: string }> {
  try {
    const { data, error } = await supabaseAdmin
      .from('form_fields')
      .insert({
        event_id: fieldData.eventId,
        field_key: fieldData.fieldKey,
        field_label: fieldData.fieldLabel,
        field_type: fieldData.fieldType,
        field_options: fieldData.fieldOptions || null,
        is_required: fieldData.isRequired || false,
        is_standard_field: fieldData.isStandardField || false,
        is_enabled: fieldData.isEnabled !== undefined ? fieldData.isEnabled : true,
        display_order: fieldData.displayOrder || 0,
        validation_rules: fieldData.validationRules || null,
        placeholder_text: fieldData.placeholderText || null,
        help_text: fieldData.helpText || null
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating form field:', error);
      return { data: null, error: error.message };
    }

    const formField: FormField = {
      id: data.id,
      eventId: data.event_id,
      fieldKey: data.field_key,
      fieldLabel: data.field_label,
      fieldType: data.field_type as FormFieldType,
      fieldOptions: data.field_options,
      isRequired: data.is_required,
      isStandardField: data.is_standard_field,
      isEnabled: data.is_enabled,
      displayOrder: data.display_order,
      validationRules: data.validation_rules,
      placeholderText: data.placeholder_text,
      helpText: data.help_text,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };

    return { data: formField };
  } catch (error) {
    console.error('Error in createFormField:', error);
    return { data: null, error: 'Failed to create form field' };
  }
}

/**
 * Update an existing form field
 */
export async function updateFormField(fieldId: string, updates: FormFieldUpdate): Promise<{ data: FormField | null; error?: string }> {
  try {
    const updateData: any = {};
    
    if (updates.fieldLabel !== undefined) updateData.field_label = updates.fieldLabel;
    if (updates.fieldType !== undefined) updateData.field_type = updates.fieldType;
    if (updates.fieldOptions !== undefined) updateData.field_options = updates.fieldOptions;
    if (updates.isRequired !== undefined) updateData.is_required = updates.isRequired;
    if (updates.isEnabled !== undefined) updateData.is_enabled = updates.isEnabled;
    if (updates.displayOrder !== undefined) updateData.display_order = updates.displayOrder;
    if (updates.validationRules !== undefined) updateData.validation_rules = updates.validationRules;
    if (updates.placeholderText !== undefined) updateData.placeholder_text = updates.placeholderText;
    if (updates.helpText !== undefined) updateData.help_text = updates.helpText;

    const { data, error } = await supabaseAdmin
      .from('form_fields')
      .update(updateData)
      .eq('id', fieldId)
      .select()
      .single();

    if (error) {
      console.error('Error updating form field:', error);
      return { data: null, error: error.message };
    }

    const formField: FormField = {
      id: data.id,
      eventId: data.event_id,
      fieldKey: data.field_key,
      fieldLabel: data.field_label,
      fieldType: data.field_type as FormFieldType,
      fieldOptions: data.field_options,
      isRequired: data.is_required,
      isStandardField: data.is_standard_field,
      isEnabled: data.is_enabled,
      displayOrder: data.display_order,
      validationRules: data.validation_rules,
      placeholderText: data.placeholder_text,
      helpText: data.help_text,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };

    return { data: formField };
  } catch (error) {
    console.error('Error in updateFormField:', error);
    return { data: null, error: 'Failed to update form field' };
  }
}

/**
 * Delete a form field (only custom fields can be deleted)
 */
export async function deleteFormField(fieldId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabaseAdmin
      .from('form_fields')
      .delete()
      .eq('id', fieldId)
      .eq('is_standard_field', false); // Only allow deletion of custom fields

    if (error) {
      console.error('Error deleting form field:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in deleteFormField:', error);
    return { success: false, error: 'Failed to delete form field' };
  }
}

/**
 * Update display order of multiple fields
 */
export async function updateFieldsOrder(fieldUpdates: { id: string; displayOrder: number }[]): Promise<{ success: boolean; error?: string }> {
  try {
    const updates = fieldUpdates.map(update => 
      supabaseAdmin
        .from('form_fields')
        .update({ display_order: update.displayOrder })
        .eq('id', update.id)
    );

    const results = await Promise.all(updates);
    
    for (const result of results) {
      if (result.error) {
        console.error('Error updating field order:', result.error);
        return { success: false, error: result.error.message };
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error in updateFieldsOrder:', error);
    return { success: false, error: 'Failed to update field order' };
  }
}

/**
 * Generate a unique field key for custom fields
 */
export function generateFieldKey(label: string): string {
  return label
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '_')
    .substring(0, 50);
}

/**
 * Validate field key uniqueness within an event
 */
export async function validateFieldKey(eventId: string, fieldKey: string, excludeId?: string): Promise<{ isValid: boolean; error?: string }> {
  try {
    let query = supabaseAdmin
      .from('form_fields')
      .select('id')
      .eq('event_id', eventId)
      .eq('field_key', fieldKey);

    if (excludeId) {
      query = query.neq('id', excludeId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error validating field key:', error);
      return { isValid: false, error: error.message };
    }

    return { isValid: data.length === 0 };
  } catch (error) {
    console.error('Error in validateFieldKey:', error);
    return { isValid: false, error: 'Failed to validate field key' };
  }
}

import { supabaseAdmin } from '$lib/server/db/supabaseAdmin.js';
import { redirect } from '@sveltejs/kit';

export async function GET({ setHeaders, locals: { safeGetSession }, params }) {
    const { session } = await safeGetSession();
    if (!session) {
        redirect(303, '/');
    }

    const { data } = await supabaseAdmin.from('guests').select('*').eq('event_id', params.eventid);

    function escapeField(field) {
        if (field === null || field === undefined) {
            return '""';
        }

        field = String(field);

        // Handle Arabic text and other special characters
        field = field.normalize('NFD');

        // Escape quotes
        if (field.includes('"')) {
            field = field.replace(/"/g, '""');
        }

        // Always wrap fields in quotes to handle special characters better
        return `"${field}"`;
    }

    // Add BOM for Excel to recognize UTF-8
    const BOM = '\uFEFF';

    if (data.length === 0) {
        return new Response('No data found', {
            headers: {
                'Content-Type': 'text/plain; charset=utf-8'
            }
        });
    }

    // Dynamically generate headers from the keys of the first row
    const headers = Object.keys(data[0]);

    // Convert data to CSV format
    const csv = BOM + [
        headers.map(escapeField).join(','),
        ...data.map((row) =>
            headers.map((header) => escapeField(row[header])).join(',')
        )
    ].join('\n');

    // Set headers with UTF-8 encoding
    setHeaders({
        'Content-Type': 'text/csv; charset=utf-8',
        'Content-Disposition': 'attachment; filename="export.csv"'
    });

    // Return the CSV data as UTF-8
    return new Response(csv, {
        headers: {
            'Content-Type': 'text/csv; charset=utf-8'
        }
    });
}
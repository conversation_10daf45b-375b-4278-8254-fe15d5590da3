import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request, locals: { supabase } }) => {
    try {
        const scanRecord = await request.json();

        console.log('scanRecord received on /api/record-invalid-scan  : ', scanRecord);

        // Insert the invalid scan record into the database
        const { data, error } = await supabase
            .from('invalid_scans')
            .insert({
                qr_data: scanRecord.qr_data,
                timestamp: scanRecord.timestamp,
                scanner_id: scanRecord.scanner_id,
                scanner_name: scanRecord.scanner_name,
                gate: scanRecord.gate,
                scan_type: scanRecord.scan_type,
                status: scanRecord.status || 'invalid'
            })
            .select()
            .single();

        if (error) {
            console.error('Error recording invalid scan:', error);
            return json({ success: false, error: error.message }, { status: 500 });
        }

        return json({
            success: true,
            scan_id: data.id
        });
    } catch (error) {
        console.error('Error in record-invalid-scan endpoint:', error);
        return json({ success: false, error: 'Internal server error' }, { status: 500 });
    }
};
import { generateUniqueId, generateReferenceCode } from '$lib/utils/generateId';

// Example usage of the generateUniqueId function
export function showIdExamples() {
    // Basic usage with default parameters
    const basicId = generateUniqueId();
    console.log('Basic ID:', basicId);
    
    // With custom length
    const longId = generateUniqueId({ length: 20 });
    console.log('Long ID (20 chars):', longId);
    
    // With prefix
    const prefixedId = generateUniqueId({ prefix: 'EVENT-', length: 8 });
    console.log('Prefixed ID:', prefixedId);
    
    // Numbers only
    const numbersOnlyId = generateUniqueId({ useLetters: false, useNumbers: true });
    console.log('Numbers only ID:', numbersOnlyId);
    
    // With special characters
    const specialId = generateUniqueId({ useSpecial: true });
    console.log('ID with special chars:', specialId);
    
    // Reference code examples
    const referenceCode = generateReferenceCode();
    console.log('Reference code:', referenceCode);
    
    const eventReferenceCode = generateReferenceCode('EVENT');
    console.log('Event reference code:', eventReferenceCode);
    
    return {
        basicId,
        longId,
        prefixedId,
        numbersOnlyId,
        specialId,
        referenceCode,
        eventReferenceCode
    };
}

<script lang="ts">
	import { Switch } from '$lib/components/ui/switch';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import { Settings } from 'lucide-svelte';
	import type { StandardField, FormField } from '$lib/types/formFields';

	// Props
	let {
		standardFieldConfig,
		field,
		onToggle,
		onEdit
	}: {
		standardFieldConfig: StandardField;
		field: FormField | undefined;
		onToggle: (enabled: boolean) => void;
		onEdit: () => void;
	} = $props();

	// Reactive values
	let isEnabled = $derived(field?.isEnabled ?? standardFieldConfig.defaultEnabled);
	let isRequired = $derived(field?.isRequired ?? standardFieldConfig.isRequired);

	// Local state for the switch to handle immediate UI updates
	let switchChecked = $state(false);

	// Sync switch state with field state
	$effect(() => {
		switchChecked = isEnabled;
	});

	function handleToggle() {
		// Update local switch state immediately for responsive UI
		switchChecked = !switchChecked;
		// Call the parent toggle handler
		onToggle(switchChecked);
	}
</script>

<div
	class="flex items-center justify-between rounded-lg border p-4 transition-colors hover:bg-muted/50"
>
	<div class="flex items-center gap-4">
		<!-- Toggle Switch -->
		<Switch
			bind:checked={switchChecked}
			onclick={handleToggle}
			aria-label="Toggle {standardFieldConfig.fieldLabel}"
		/>

		<!-- Field Info -->
		<div class="flex-1">
			<div class="mb-1 flex items-center gap-2">
				<span class="font-medium">{standardFieldConfig.fieldLabel}</span>

				<!-- Badges -->
				{#if isRequired}
					<Badge variant="destructive" class="text-xs">Required</Badge>
				{/if}

				{#if !switchChecked}
					<Badge variant="secondary" class="text-xs">Disabled</Badge>
				{/if}

				<Badge variant="outline" class="text-xs">
					{standardFieldConfig.fieldType}
				</Badge>
			</div>

			<div class="text-sm text-muted-foreground">
				<p>{standardFieldConfig.description}</p>
				<p class="mt-1 text-xs">
					Field key: <code class="rounded bg-muted px-1">{standardFieldConfig.fieldKey}</code>
				</p>
			</div>
		</div>
	</div>

	<!-- Actions -->
	<div class="flex items-center gap-2">
		<!-- Info tooltip could be added here -->
		<Button
			variant="ghost"
			size="sm"
			onclick={onEdit}
			disabled={!field}
			title="Edit field settings"
		>
			<Settings class="h-4 w-4" />
		</Button>
	</div>
</div>

<style>
	code {
		font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
		font-size: 0.85em;
	}
</style>

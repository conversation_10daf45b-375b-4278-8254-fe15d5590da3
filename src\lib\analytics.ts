// Analytics wrapper that provides a consistent interface for tracking
import {
	initPostHog,
	trackEvent,
	trackPageView,
	trackButtonClick,
	trackFormSubmit,
	trackLinkClick,
	trackError,
	trackPerformance,
	identifyUser,
	setUserProperties,
	resetUser,
	getFeatureFlag,
	isFeatureEnabled,
	isPostHogAvailable
} from '$lib/posthog';

// Initialize analytics
export function initAnalytics(): void {
	initPostHog();
}

// Check if analytics is ready
export function isAnalyticsReady(): boolean {
	return isPostHogAvailable();
}

// Analytics interface
export const analytics = {
	// Initialization
	init: initAnalytics,
	isReady: isAnalyticsReady,

	// Event tracking
	trackEvent,
	trackPageView,
	trackButtonClick,
	trackFormSubmit,
	trackLinkClick,
	trackError,
	trackPerformance,

	// User management
	identifyUser,
	setUserProperties,
	resetUser,

	// Feature flags
	getFeatureFlag,
	isFeatureEnabled,

	// Admin actions tracking
	trackAdminAction: (action: string, properties?: Record<string, any>) => {
		trackEvent('admin_action', {
			action,
			timestamp: new Date().toISOString(),
			...properties
		});
	},

	// Form events tracking
	trackFormEvent: (eventType: string, formName: string, properties?: Record<string, any>) => {
		trackEvent('form_event', {
			event_type: eventType,
			form_name: formName,
			timestamp: new Date().toISOString(),
			...properties
		});
	},

	// API error tracking
	trackApiError: (endpoint: string, error: Error, statusCode?: number) => {
		trackEvent('api_error', {
			endpoint,
			error_message: error.message,
			error_stack: error.stack,
			status_code: statusCode,
			timestamp: new Date().toISOString()
		});
	},

	// Performance tracking helpers
	trackPageLoadTime: (loadTime: number) => {
		trackPerformance('page_load_time', loadTime, {
			timestamp: new Date().toISOString()
		});
	},

	trackApiResponseTime: (endpoint: string, responseTime: number) => {
		trackPerformance('api_response_time', responseTime, {
			endpoint,
			timestamp: new Date().toISOString()
		});
	},

	// Admin-specific tracking
	trackAdminPageView: (adminPage: string, eventId?: string) => {
		trackEvent('admin_page_view', {
			admin_page: adminPage,
			event_id: eventId,
			timestamp: new Date().toISOString()
		});
	},

	// Authentication tracking
	trackLoginAttempt: (email: string) => {
		trackEvent('login_attempt', {
			email,
			timestamp: new Date().toISOString()
		});
	},

	trackLoginSuccess: (userId: string) => {
		trackEvent('login_success', {
			user_id: userId,
			timestamp: new Date().toISOString()
		});
	},

	trackLoginFailure: (errorMessage: string) => {
		trackEvent('login_failure', {
			error_message: errorMessage,
			timestamp: new Date().toISOString()
		});
	},

	trackLogout: () => {
		trackEvent('logout', {
			timestamp: new Date().toISOString()
		});
	},

	// Guest list tracking
	trackGuestListViewed: (eventId: string, guestCount: number) => {
		trackEvent('guest_list_viewed', {
			event_id: eventId,
			guest_count: guestCount,
			timestamp: new Date().toISOString()
		});
	}
};

// Export individual functions for convenience
export {
	trackEvent,
	trackPageView,
	trackButtonClick,
	trackFormSubmit,
	trackLinkClick,
	trackError,
	trackPerformance,
	identifyUser,
	setUserProperties,
	resetUser,
	getFeatureFlag,
	isFeatureEnabled
};

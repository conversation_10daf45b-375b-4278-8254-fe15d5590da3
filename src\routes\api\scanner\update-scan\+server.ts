import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';

export const POST: RequestHandler = async ({ request, locals: { supabase } }) => {


    try {
        const updateData = await request.json();


        console.log('updateData received on /api/update-scan  : ', updateData);

        // Validate required fields
        if (!updateData.scanner_id || !updateData.scan_type) {
            return json({
                success: false,
                error: 'Missing required fields: scan_id and scan_type are required'
            }, { status: 400 });
        }

        // Update the scan record in the database
        // const { data, error } = await supabase
        //     .from('scans')
        //     .update({
        //         scan_type: updateData.scan_type
        //     })
        //     .eq('scanner_id', updateData.scanner_id)
        //     .select()
        //     .single();

        // if (error) {
        //     console.error('Error updating scan:', error);
        //     return json({ success: false, error: error.message }, { status: 500 });
        // }

        // New Logic for Working in Scale: 
        // Issue: When the check-in or check-out is updated need to get the row that was added. as we are using the qr_data and the Scanner_id to update the scan_type.
        // There is a possibility that there will be multiple scans of the data. Which will cases can issue. 
        // My Thought process: 
        // Can add a scan_signature to each scan and will change on each scan. can use a scannerId_datetime to make it unique. This is harder
        // OR 
        // Can just check the last entry of the scanner_id and the qr_data based on the created_at timestamp. This is easier. Assuming the users don't share the usernames. 
        // 


        // OPTION 2: Easy Method
        // Step 1: Get the latest scan record for the scanner_id
        const { data: latestScan, error: fetchError } = await supabase
            .from('scans')
            .select('id')
            .eq('scanner_id', updateData.scanner_id)
            .order('id', { ascending: false })
            .limit(1)
            .single();

        if (fetchError || !latestScan) {
            console.error('Error fetching latest scan:', fetchError);
            return json({ success: false, error: 'Latest scan not found' }, { status: 404 });
        }

        // Step 2: Update that scan record
        const { data, error: updateError } = await supabase
            .from('scans')
            .update({ scan_type: updateData.scan_type })
            .eq('id', latestScan.id)
            .select()
            .single();

        if (updateError) {
            console.error('Error updating scan:', updateError);
            return json({ success: false, error: updateError.message }, { status: 500 });
        }

        return json({
            success: true,
            data: data
        });
    } catch (error) {
        console.error('Error in update-scan endpoint:', error);
        return json({ success: false, error: 'Internal server error' }, { status: 500 });
    }
};
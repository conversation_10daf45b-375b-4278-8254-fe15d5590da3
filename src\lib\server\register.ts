
import { generateQR } from './generateqr';
// import { getFormattedQatarTime } from '$lib/utils/qatartime'
import { supabase } from '$lib/db/supabaseClient'
import { sendEmailTemplate } from '$lib/server/sendEmail';
import { getRegistrationStatus } from './registrationStatus';



export const registerUser = async (form) => {

    // console.log('Form Data received from +page.server.ts to Register Function: ', form.data)
    // const formattedQatarTime = await getFormattedQatarTime()
    // console.log('Formatted Time: ', formattedQatarTime)

    // Check registration status (including automatic closure)
    const registrationStatus = await getRegistrationStatus(supabase, form.data.eventId);

    if (!registrationStatus.isOpen) {
        return { success: false, message: registrationStatus.reason || 'Registration is closed' };
    }

    // Check if user already exists in Users Table 
    const { data: userData, error: userDataError } = await supabase
        .from('users')
        .select('email')
        .eq('email', form.data.email)

    if (userDataError) {
        console.error('Error in User Data Query: ', userDataError);
        // return
    }

    if (userData && userData.length > 0) {
        console.log('User found in DB')
        // return
    } else {
        console.log('User not found in DB')
        console.log('Add User Data to Users Table ')
        const { error: userInsertError, data: userInsertData } = await supabase.from('users').insert({
            name: form.data.firstName + ' ' + form.data.lastName,
            // name: form.data.name,
            email: form.data.email,
            mobile: form.data.mobile,
            organization: form.data.organization,
        }).select();

        if (userInsertError) {
            console.error('Error writing data to User table.', userInsertError)
            // return
        }

        // console.log('User Data Inserted: ', userInsertData);
    }

    // Check if registration capacity is full
    const { data: count, error: regError } = await supabase
        .from('guests')
        .select('count', { count: 'exact' })
        .eq('event_id', form.data.eventId)
        .in('status', ['registered', 'imported']);

    // console.log('Count: ', count)

    // Read Event Data from Events Table
    const { data: eventCapacity, error: eventCapacityError } = await supabase
        .from('events')
        .select('max_registrations, waiting_list')
        .eq('id', form.data.eventId)

    if (eventCapacityError) {
        console.error('Error in Event Data Query: ', eventCapacityError);
        // return
    }

    // console.log('Check Capacity: ', eventCapacity)

    let waitingList = eventCapacity[0].waiting_list && count[0].count > eventCapacity[0].max_registrations;

    // console.log('Waiting List from condition: ', waitingList)

    if (waitingList) {
        console.log('Waiting List is full')
        // return { success: false, message: 'Event is full. Please try again later' }

        // Add user data to guests table
        const { error: insertError, data: insertData } = await supabase.from('guests').insert({
            first_name: form.data.firstName,
            last_name: form.data.lastName,
            // name: form.data.name,
            email: form.data.email,
            mobile: form.data.mobile,
            organization: form.data.organization,
            event_id: form.data.eventId,
            gender: form.data.gender,
            nationality_form: form.data.nationality,
            business: form.data.business,
            category: form.data.category,
            stage: form.data.stage,
            know: form.data.know,
            status: 'waiting',
        }).select();

        if (insertError) {
            console.error('Error writing form data to guests table.', insertError)
            // return
        }

        // console.log('Data Inserted: ', insertData);


        console.log('QR Data Generation started.')
        let combinedQRData = `${insertData[0].email}`
        console.log('QR Code Generation Data: ', combinedQRData)
        console.log('QR Data Generation completed')


        // Generate and Save the QR using in a Public bucket
        // console.log('Generating QR Code.')
        const result = await generateQR(combinedQRData, insertData[0].uid)

        console.log('QR Code Result from response: ', result)
        // if (!response.ok) {
        //     throw new Error(result.error || 'Failed to generate QR code');
        // }

        const publicQRUrl = result.url
        // console.log('Finished Generating QR Code.')

        console.log('Updating Guest Data with QR in Guests Table')
        const { error: updateError, data: updateData } = await supabase.from('guests').update({
            qr_url: publicQRUrl,

        }).eq('id', insertData[0].id)

        if (updateError) {
            console.error('Error updating data to guests table.', updateError)
            // return { success: false, message: 'Error updating guest data' }
        }
        console.log('Guest Data Updated.');

        // Read Event Data from Events Table 
        const { data: eventData, error: eventDataError } = await supabase
            .from('events')
            .select('title, start_date, end_date, start_time, end_time, location, location_url')
            .eq('id', form.data.eventId)

        if (eventDataError) {
            console.error('Error in Event Data Query: ', eventDataError);
            // return
        }

        const event = eventData[0]
        // console.log('Event Data: ', event)


        // console.log('Sending Email to Guest')
        // sendEmailTemplate(form.data.name, form.data.email, publicQRUrl, event.title, event.start_date, event.end_date, event.start_time, event.end_time, event.location, event.location_url) // Send event name, date, time, location, location url,
        // console.log('Email trigger completed');

        return { success: true, message: 'Registration Successful. You are on a waiting list. An email will be sent to you upon approval.' }

    } else {
        console.log('Waiting List is not full')
        // Add user data to guests table
        const { error: insertError, data: insertData } = await supabase.from('guests').insert({
            first_name: form.data.firstName,
            last_name: form.data.lastName,
            // name: form.data.name,
            email: form.data.email,
            mobile: form.data.mobile,
            organization: form.data.organization,
            event_id: form.data.eventId,
            gender: form.data.gender,
            nationality_form: form.data.nationality,
            business: form.data.business,
            category: form.data.category,
            stage: form.data.stage,
            know: form.data.know,
        }).select();

        if (insertError) {
            console.error('Error writing form data to guests table.', insertError)
            // return
        }

        // console.log('Data Inserted: ', insertData);


        console.log('QR Data Generation started.')
        let combinedQRData = `${insertData[0].email}`
        console.log('QR Code Generation Data: ', combinedQRData)
        console.log('QR Data Generation completed')


        // Generate and Save the QR using in a Public bucket
        console.log('Generating QR Code.')
        const result = await generateQR(combinedQRData, insertData[0].uid)

        // console.log('QR Code Result from response: ', result)
        // if (!response.ok) {
        //     throw new Error(result.error || 'Failed to generate QR code');
        // }

        const publicQRUrl = result.url
        console.log('Finished Generating QR Code.')

        console.log('Updating Guest Data with QR in Guests Table')
        const { error: updateError, data: updateData } = await supabase.from('guests').update({
            qr_url: publicQRUrl,

        }).eq('id', insertData[0].id)

        if (updateError) {
            console.error('Error updating data to guests table.', updateError)
            // return { success: false, message: 'Error updating guest data' }
        }
        console.log('Guest Data Updated.');

        // Read Event Data from Events Table 
        const { data: eventData, error: eventDataError } = await supabase
            .from('events')
            .select('title, start_date, end_date, start_time, end_time, location, location_url')
            .eq('id', form.data.eventId)

        if (eventDataError) {
            console.error('Error in Event Data Query: ', eventDataError);
            // return
        }

        const event = eventData[0]
        // console.log('Event Data: ', event)


        console.log('Sending Email to Guest')
        sendEmailTemplate(form.data.firstName, form.data.lastName, form.data.email, publicQRUrl, event.title, event.start_date, event.end_date, event.start_time, event.end_time, event.location, event.location_url) // Send event name, date, time, location, location url,
        console.log('Email trigger completed');

        return { success: true, message: 'Registration Successful' }
    }




}


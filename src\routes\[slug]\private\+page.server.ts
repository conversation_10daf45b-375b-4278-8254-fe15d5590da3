import { error, redirect, type Actions } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals: { supabase }, url, cookies }) => {
	const { slug } = params;

	// Get private event data (only basic info for access page)
	const { data: eventArray, error: eventsError } = await supabase
		.from('events')
		.select('id, title, short_description, is_published, is_private, active_code')
		.eq('title_slug', slug)
		.eq('is_published', true)
		.eq('is_private', true)
		.single();

	if (eventsError || !eventArray) {
		throw error(404, 'Event not found');
	}

	// Check if user already has valid access (secure server-side check)
	const verificationCookie = cookies.get(`private_event_${eventArray.id}`);
	if (verificationCookie === eventArray.active_code) {
		// User already has valid access, redirect to main event page
		redirect(303, `/${slug}`);
	}

	return {
		event: {
			title: eventArray.title,
			short_description: eventArray.short_description
		},
		error: url.searchParams.get('error')
	};
};

export const actions: Actions = {
	verifyAccess: async ({ params, request, locals: { supabase }, cookies }) => {
		const { slug } = params;
		const formData = await request.formData();
		const accessCode = formData.get('access_code') as string;

		if (!accessCode) {
			redirect(303, `/${slug}/private?error=Access code is required`);
		}

		// Get event with access code
		const { data: eventData, error: eventError } = await supabase
			.from('events')
			.select('id, active_code')
			.eq('title_slug', slug)
			.eq('is_published', true)
			.eq('is_private', true)
			.single();

		if (eventError || !eventData) {
			throw error(404, 'Event not found');
		}

		// Verify access code
		if (accessCode !== eventData.active_code) {
			redirect(303, `/${slug}/private?error=Invalid access code`);
		}

		// Valid access code - set secure cookie for this specific event
		cookies.set(`private_event_${eventData.id}`, eventData.active_code, {
			path: '/',
			httpOnly: true,
			secure: true,
			sameSite: 'strict',
			maxAge: 60 * 60 * 24 * 7 // 7 days
		});

		// Redirect to main event page
		redirect(303, `/${slug}`);
	}
};

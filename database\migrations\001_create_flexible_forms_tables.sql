-- Migration: Create flexible forms tables
-- Description: Add support for dynamic form fields and responses per event

-- Create form_fields table to store field configurations per event
CREATE TABLE IF NOT EXISTS form_fields (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    field_key VARCHAR(100) NOT NULL,
    field_label VARCHAR(255) NOT NULL,
    field_type VARCHAR(50) NOT NULL CHECK (field_type IN ('text', 'email', 'phone', 'select', 'checkbox', 'textarea', 'number', 'date', 'url')),
    field_options JSONB DEFAULT NULL,
    is_required BOOLEAN DEFAULT false,
    is_standard_field BOOLEAN DEFAULT false,
    is_enabled BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    validation_rules JSONB DEFAULT NULL,
    placeholder_text VARCHAR(255) DEFAULT NULL,
    help_text TEXT DEFAULT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(event_id, field_key)
);

-- Create form_responses table to store user responses
CREATE TABLE IF NOT EXISTS form_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    guest_id INTEGER NOT NULL REFERENCES guests(id) ON DELETE CASCADE,
    field_key VARCHAR(100) NOT NULL,
    field_value TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(guest_id, field_key)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_form_fields_event_id ON form_fields(event_id);
CREATE INDEX IF NOT EXISTS idx_form_fields_enabled ON form_fields(event_id, is_enabled);
CREATE INDEX IF NOT EXISTS idx_form_fields_display_order ON form_fields(event_id, display_order);
CREATE INDEX IF NOT EXISTS idx_form_responses_guest_id ON form_responses(guest_id);
CREATE INDEX IF NOT EXISTS idx_form_responses_event_field ON form_responses(event_id, field_key);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_form_fields_updated_at 
    BEFORE UPDATE ON form_fields 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_form_responses_updated_at 
    BEFORE UPDATE ON form_responses 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to initialize standard fields for an event
CREATE OR REPLACE FUNCTION initialize_standard_fields(p_event_id UUID)
RETURNS VOID AS $$
BEGIN
    INSERT INTO form_fields (event_id, field_key, field_label, field_type, is_standard_field, is_required, is_enabled, display_order) VALUES
        (p_event_id, 'first_name', 'First Name', 'text', true, true, true, 1),
        (p_event_id, 'middle_name', 'Middle Name', 'text', true, false, false, 2),
        (p_event_id, 'last_name', 'Last Name', 'text', true, true, true, 3),
        (p_event_id, 'full_name', 'Full Name', 'text', true, false, false, 4),
        (p_event_id, 'email', 'Email Address', 'email', true, true, true, 5),
        (p_event_id, 'mobile', 'Mobile Number', 'phone', true, false, true, 6),
        (p_event_id, 'whatsapp', 'WhatsApp Number', 'phone', true, false, false, 7),
        (p_event_id, 'organization', 'Company/Organization', 'text', true, false, true, 8),
        (p_event_id, 'designation', 'Designation/Role', 'text', true, false, false, 9)
    ON CONFLICT (event_id, field_key) DO NOTHING;
END;
$$ LANGUAGE plpgsql;

-- Function to get form fields for an event
CREATE OR REPLACE FUNCTION get_event_form_fields(p_event_id UUID)
RETURNS TABLE (
    id UUID,
    field_key VARCHAR(100),
    field_label VARCHAR(255),
    field_type VARCHAR(50),
    field_options JSONB,
    is_required BOOLEAN,
    is_standard_field BOOLEAN,
    is_enabled BOOLEAN,
    display_order INTEGER,
    validation_rules JSONB,
    placeholder_text VARCHAR(255),
    help_text TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ff.id,
        ff.field_key,
        ff.field_label,
        ff.field_type,
        ff.field_options,
        ff.is_required,
        ff.is_standard_field,
        ff.is_enabled,
        ff.display_order,
        ff.validation_rules,
        ff.placeholder_text,
        ff.help_text
    FROM form_fields ff
    WHERE ff.event_id = p_event_id AND ff.is_enabled = true
    ORDER BY ff.display_order ASC, ff.created_at ASC;
END;
$$ LANGUAGE plpgsql;

-- Function to get guest responses with field information
CREATE OR REPLACE FUNCTION get_guest_form_responses(p_guest_id INTEGER)
RETURNS TABLE (
    field_key VARCHAR(100),
    field_label VARCHAR(255),
    field_type VARCHAR(50),
    field_value TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ff.field_key,
        ff.field_label,
        ff.field_type,
        fr.field_value
    FROM form_responses fr
    JOIN form_fields ff ON ff.field_key = fr.field_key AND ff.event_id = fr.event_id
    WHERE fr.guest_id = p_guest_id
    ORDER BY ff.display_order ASC;
END;
$$ LANGUAGE plpgsql;

-- Add RLS policies for security
ALTER TABLE form_fields ENABLE ROW LEVEL SECURITY;
ALTER TABLE form_responses ENABLE ROW LEVEL SECURITY;

-- Policy for form_fields: Allow authenticated users to read, admins to modify
CREATE POLICY "Allow authenticated users to read form fields" ON form_fields
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow admins to manage form fields" ON form_fields
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE auth_id = auth.uid()
        )
    );

-- Policy for form_responses: Allow users to insert their own responses, admins to read all
CREATE POLICY "Allow users to insert form responses" ON form_responses
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow admins to read form responses" ON form_responses
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE auth_id = auth.uid()
        )
    );

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT ON form_fields TO authenticated;
GRANT INSERT ON form_responses TO authenticated;
GRANT ALL ON form_fields TO service_role;
GRANT ALL ON form_responses TO service_role;

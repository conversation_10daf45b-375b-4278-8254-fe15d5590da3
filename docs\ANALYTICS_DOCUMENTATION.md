# PostHog Analytics Implementation Documentation

## Overview

This document describes the comprehensive PostHog analytics implementation for the SvelteKit Event Management Application. The implementation tracks user interactions, admin actions, form events, errors, and performance metrics across the entire application.

## Configuration

### Environment Variables

Add these environment variables to your `.env` file:

```env
PUBLIC_POSTHOG_API_KEY=phc_MtdeHEDSmDcmQCPE420NjptCwvUUpS1wSYEtXlzBdn7
PUBLIC_POSTHOG_HOST=https://us.i.posthog.com
```

### PostHog Features Enabled

- **Product Analytics**: Track user interactions and behavior
- **Web Analytics**: Automatic page view tracking
- **Session Replay**: Record user sessions (with input masking for privacy)
- **Error Tracking**: Capture JavaScript errors and API failures

## Architecture

### Core Files

- `src/lib/analytics/posthog.ts` - PostHog client initialization and configuration
- `src/lib/analytics/analytics.ts` - Centralized analytics service with all tracking methods
- `src/lib/analytics/errorTracking.ts` - Enhanced error tracking and performance monitoring utilities

### Initialization

Analytics are initialized in `src/routes/+layout.svelte` on application startup:

```typescript
import { initPostHog } from '$lib/analytics/posthog';
import { initializePerformanceTracking } from '$lib/analytics/errorTracking';

onMount(() => {
    initPostHog();
    initializePerformanceTracking();
});
```

## Tracked Events

### Page Views

**Event**: `$pageview`
**Triggered**: Automatically on route changes
**Properties**:
- `$current_url`: Current page path
- `page_title`: Document title
- `referrer`: Previous page URL

### Authentication Events

#### Login Attempt
**Event**: `login_attempt`
**Triggered**: When user submits login form
**Properties**:
- `email`: User's email address

#### Login Success
**Event**: `login_success`
**Triggered**: When user successfully logs into admin panel
**Properties**:
- `user_id`: Authenticated user ID

#### Login Failure
**Event**: `login_failure`
**Triggered**: When login fails
**Properties**:
- `error`: Error message

#### Logout
**Event**: `logout`
**Triggered**: When user logs out
**Properties**: None

### Event Page Interactions

#### Event Page View
**Event**: `event_page_view`
**Triggered**: When user visits a public event page
**Properties**:
- `event_id`: Event ID
- `event_slug`: Event URL slug
- `event_title`: Event title

#### Event Published
**Event**: `admin_event_published`
**Triggered**: When admin publishes an event
**Properties**:
- `event_id`: Event ID

#### Link Clicks
**Event**: `link_click`
**Triggered**: When user clicks tracked links
**Properties**:
- `link_text`: Link text
- `link_url`: Link destination
- `location`: Where the link was clicked

### Registration Form Events

#### Form Started
**Event**: `form_started`
**Triggered**: When user first interacts with registration form
**Properties**:
- `event_id`: Event ID
- `form_type`: Type of form (usually 'registration')

#### Form Field Interaction
**Event**: `form_field_interaction`
**Triggered**: When user interacts with a form field for the first time
**Properties**:
- `event_id`: Event ID
- `field_key`: Field identifier
- `field_type`: Type of field (text, email, etc.)

#### Form Validation Error
**Event**: `form_validation_error`
**Triggered**: When form validation fails
**Properties**:
- `event_id`: Event ID
- `field_key`: Field that failed validation
- `error_message`: Validation error message

#### Form Submission Attempt
**Event**: `form_submission_attempt`
**Triggered**: When user attempts to submit form
**Properties**:
- `event_id`: Event ID
- `form_type`: Type of form

#### Form Submission Success
**Event**: `form_submission_success`
**Triggered**: When form is successfully submitted
**Properties**:
- `event_id`: Event ID
- `registration_id`: Generated registration ID

#### Form Submission Failure
**Event**: `form_submission_failure`
**Triggered**: When form submission fails
**Properties**:
- `event_id`: Event ID
- `error`: Error message

#### Form Abandoned
**Event**: `form_abandoned`
**Triggered**: When user leaves page without completing form
**Properties**:
- `event_id`: Event ID
- `fields_completed`: Number of fields filled
- `total_fields`: Total number of fields
- `completion_rate`: Percentage of form completed

### Admin Panel Events

#### Admin Page View
**Event**: `admin_page_view`
**Triggered**: When admin visits any admin page
**Properties**:
- `admin_page`: Page identifier
- `event_id`: Event ID (if applicable)

#### Event Created
**Event**: `admin_event_created`
**Triggered**: When admin creates a new event
**Properties**:
- `event_id`: Created event ID
- `event_title`: Event title

#### Event Updated
**Event**: `admin_event_updated`
**Triggered**: When admin updates an event
**Properties**:
- `event_id`: Event ID
- `changed_fields`: Array of modified fields

#### Form Builder Used
**Event**: `admin_form_builder_used`
**Triggered**: When admin uses form builder
**Properties**:
- `event_id`: Event ID
- `action`: Action taken (field_added, field_removed, etc.)

### Guest Management Events

#### Guest List Viewed
**Event**: `admin_guest_list_viewed`
**Triggered**: When admin views guest list
**Properties**:
- `event_id`: Event ID
- `guest_count`: Number of guests

#### Guest Data Exported
**Event**: `admin_guest_data_exported`
**Triggered**: When admin exports guest data
**Properties**:
- `event_id`: Event ID
- `export_type`: Export format (csv, pdf)

#### QR Code Scanned
**Event**: `admin_qr_code_scanned`
**Triggered**: When admin scans QR code for check-in/out
**Properties**:
- `event_id`: Event ID
- `scan_type`: Type of scan (check_in, check_out)

### Button and Interaction Tracking

#### Button Click
**Event**: `button_click`
**Triggered**: When user clicks tracked buttons
**Properties**:
- `button_name`: Descriptive button name
- `location`: Where button was clicked
- Additional context properties

### Error Tracking

#### JavaScript Error
**Event**: `javascript_error`
**Triggered**: When JavaScript error occurs
**Properties**:
- `error_message`: Error message
- `error_filename`: File where error occurred
- `error_lineno`: Line number
- `error_colno`: Column number
- `error_stack`: Stack trace

#### API Error
**Event**: `api_error`
**Triggered**: When API request fails
**Properties**:
- `endpoint`: API endpoint
- `status_code`: HTTP status code
- `error_message`: Error message

#### General Error
**Event**: `error_occurred`
**Triggered**: For various application errors
**Properties**:
- `error_type`: Type of error
- `error_message`: Error message
- Additional context properties

### Performance Tracking

#### Performance Metric
**Event**: `performance_metric`
**Triggered**: When performance metrics are recorded
**Properties**:
- `metric`: Metric name
- `value`: Metric value
- Additional context properties

## User Identification

Admin users are automatically identified when they log in:

```typescript
analytics.identifyUser(userId, {
    email: userEmail,
    role: 'admin'
});
```

## Testing

Visit `/analytics-test` to test all analytics events and verify they're working correctly. This page provides:

- Analytics status check
- Individual event testing
- Comprehensive test suite
- Real-time results display
- Instructions for verification in PostHog dashboard

## Privacy Considerations

- Input fields are masked in session recordings for privacy
- Email addresses are only tracked for admin users
- Sensitive form data is not included in analytics events
- Error tracking excludes sensitive information

## Maintenance

### Adding New Events

1. Add method to `AnalyticsService` class in `src/lib/analytics/analytics.ts`
2. Import and call the method where the event occurs
3. Update this documentation
4. Test the new event using the analytics test page

### Debugging

- Check browser console for PostHog loading status
- Use PostHog's debug mode by adding `?debug=1` to URL
- Monitor network requests to PostHog endpoints
- Use the analytics test page to verify events

## PostHog Dashboard

Access your analytics at: https://us.i.posthog.com

Key sections to monitor:
- **Events**: See all tracked events in real-time
- **Insights**: Create custom analytics dashboards
- **Persons**: View identified users and their properties
- **Session Recordings**: Watch user sessions
- **Feature Flags**: A/B testing capabilities (not currently used)

import type { ColumnDef } from "@tanstack/table-core";
import { createRawSnippet } from "svelte";
import { renderComponent, renderSnippet } from "$lib/components/ui/data-table/index.js";
import DataTableActions from "./data-table-actions.svelte";

// This type is used to define the shape of our data.
// You can use a Zod schema here if you want.
export type Guest = {
    id: string;
    name: string;
    email: string;
    mobile: string;
    organization: string;
    status: string;

};

export const columns: ColumnDef<Guest>[] = [

    {
        accessorKey: "name",
        header: "Name",
    },

    {
        accessorKey: "email",
        header: "Email",
    },
    {
        accessorKey: "mobile",
        header: "Mobile",
    },
    {
        accessorKey: "organization",
        header: "Organization",
    },
    // {
    //     accessorKey: "status",
    //     header: "Status",
    // },
    {
        accessorKey: "status",
        header: () => {
            const statusHeaderSnippet = createRawSnippet(() => ({
                render: () => `<div>Status</div>`,
            }));
            return renderSnippet(statusHeaderSnippet, "");
        },
        cell: ({ row }) => {

            const amountCellSnippet = createRawSnippet(() => ({
                render: () => `<div><span class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10">${row.getValue("status")}</span></div>`,
            }));
            return renderSnippet(amountCellSnippet, "");

        },
    },

    {
        id: "actions",
        cell: ({ row }) => {
            // You can pass whatever you need from `row.original` to the component
            return renderComponent(DataTableActions, { id: row.original.id });
        },
    },
];
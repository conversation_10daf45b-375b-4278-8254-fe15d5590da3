// import { getFormattedQatarTime } from '$lib/utils/qatartime';
import { generateQR } from '$lib/server/generateqr';
import { supabaseAdmin } from '$lib/server/db/supabaseAdmin';
import { sendEmailTemplate } from './sendEmail';

function delay(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

export async function sendEmails(csvData, eventId) {
    let totalEmailCount = 0;
    let successEmailCount = 0;
    let failEmailCount = 0;
    // let sleeptime = 1000; // milliseconds 1000ms = 1s
    // console.log('Event ID: ', eventId)
    const { eventid } = eventId
    console.log('Event ID Extracted: ', eventid)
    console.info('Email Function Started....')
    console.log('CSV Data From Email fucntion: ', csvData)

    if (!csvData || !Array.isArray(csvData)) {
        return {
            success: false,
            message: 'Invalid CSV data format'
        };
    }

    if (csvData.length === 0) {
        return {
            success: false,
            message: 'CSV data is empty'
        };
    }
    // const formattedQatarTime = await getFormattedQatarTime()

    // Loop over each row in the CSV data and send emails to each
    for (const row of csvData) {
        if (!row.email) {
            return {
                success: false,
                message: 'Missing email address in CSV data'
            };
        }

        // Make the QR required for the QR Code

        console.log('QR Data Generation started.')
        let combinedQRData = `${row.email}`
        console.log('QR Code DATA Combined: ', combinedQRData)
        console.log('QR Data Generation Completed')

        // Generate and Save the QR using in a Public bucket
        console.log('Generating QR Code')
        let salt = Math.floor(Math.random() * 1000000); // Adjust range as needed
        let result = await generateQR(combinedQRData, salt)
        console.log('QR Code Result from response: ', result)
        let publicUrl = result.url
        console.log('Public QR URL: ', publicUrl)
        console.log('Finished Generating QR Code.')

        // Saving the  data to db
        console.info('Saving to DB')
        const { error: insertError, data: guestData } = await supabaseAdmin.from('guests').insert({
            name: row.name,
            email: row.email,
            mobile: row.mobile,
            organization: row.organization,
            qr_url: publicUrl,
            status: 'imported',
            event_id: eventid,
            // created_at: formattedQatarTime,
        }).select();

        if (insertError) {
            console.error('Error writing data to database.', insertError)
            // return
        }

        console.log('Guest Data Read: ', guestData);
        console.log('Data Inserted.');



        // Check if user already exists in Users Table 
        const { data: userData, error: userDataError } = await supabaseAdmin
            .from('users')
            .select('email')
            .eq('email', row.email)

        if (userDataError) {
            console.error('Error in User Data Query: ', userDataError);
            // return
        }

        if (userData && userData.length > 0) {
            console.log('User found in DB')
            // return
        } else {
            console.log('User not found in DB')
            console.log('Add User Data to Users Table ')
            const { error: userInsertError, data: userInsertData } = await supabaseAdmin.from('users').insert({
                name: row.name,
                email: row.email,
                mobile: row.mobile,
                organization: row.organization,

            }).select();

            if (userInsertError) {
                console.error('Error writing data to User table.', userInsertError)
                // return
            }

            console.log('User Data Inserted: ', userInsertData);

        }

        const { data: eventData, error: evnetDataError } = await supabaseAdmin
            .from('events')
            .select('title, start_date, end_date, start_time, end_time, location, location_url')
            .eq('id', eventid)

        if (evnetDataError) {
            console.error('Error in Event Data Query: ', evnetDataError);
            // return
        }

        // Send full data to Dijin DB
        // console.log('Sending Data to dijin')
        // const { error: readErrorSendData, data: readSendData } = await supabaseAdmin.from('guests').select().eq('email', form.data.email)

        // if (readErrorSendData) {
        //     console.error('Error reading data from database.', readErrorSendData)
        //     // return
        // }

        // const response = await fetch('https://us-central1-eharisdev-b9a33.cloudfunctions.net/addmessagebody', {
        //     method: 'POST',
        //     headers: {
        //         'Content-Type': 'application/json',
        //         'Authorization': `Bearer ${DIJIN_TOKEN}`
        //     },
        //     // body: jsonData1
        //     body: JSON.stringify(readSendData[0])
        // })


        // console.log('Dijins Response: ', response)

        // await delay(sleeptime);

        // Send email via SMTP
        // let responseFromEmailServer = await sendEmailSMTP(row.firstname, row.lastname, row.email, publicUrl)
        // console.log('Response from Email Server: ', responseFromEmailServer)
        // console.log('Storing Email response to DB')
        // if (responseFromEmailServer.accepted.length > 0) {
        //     const { error } = await supabaseAdmin
        //         .from('guests')
        //         .update({
        //             email_status: 'success',
        //             email_response: responseFromEmailServer
        //         })
        //         .eq('email', row.email)


        //     if (error) {
        //         console.error('Error reading data from database.', error)
        //         // return
        //     }
        //     console.log('Data Inserted.');
        //     console.log('Email sent successfully')
        //     successEmailCount++;
        // } else {
        //     const { error } = await supabaseAdmin
        //         .from('guests')
        //         .update({
        //             email_status: 'fail',
        //             email_response: responseFromEmailServer
        //         })
        //         .eq('email', row.email)


        //     if (error) {
        //         console.error('Error reading data from database.', error)
        //         // return
        //     }
        //     console.error('Error sending emai.')
        //     failEmailCount++;

        // }
        // totalEmailCount++;
        // Send email via SMTP End
        // return {};


        // console.log('=====================================================')
        // console.log('Name: ', guestData[0].name)
        // console.log('Email: ', guestData[0].email)
        // console.log('Public Url: ', publicUrl)
        // console.log('Event: ', eventData[0].title)
        // console.log('Start Date: ', eventData[0].start_date)
        // console.log('End Date: ', eventData[0].end_date)
        // console.log('Start Time: ', eventData[0].start_time)
        // console.log('End Time: ', eventData[0].end_time)
        // console.log('Location: ', eventData[0].location)
        // console.log('Location Url: ', eventData[0].location_url)
        // console.log('=====================================================')


        console.log('Sending Email to Guest')
        let zeptoResult = await sendEmailTemplate(guestData[0].name, guestData[0].email, publicUrl, eventData[0].title, eventData[0].start_date, eventData[0].end_date, eventData[0].start_time, eventData[0].end_time, eventData[0].location, eventData[0].location_url) // Send event name, date, time, location, location url,

        console.log('=====================================================')
        console.log('Zepto Response: ', zeptoResult)
        console.log('=====================================================')

        console.log('Email trigger complted');

    }

    console.log('From Email Server Backend.')
    console.info(`Total of ${totalEmailCount} Emails triggered. Success: ${successEmailCount} Fail: ${failEmailCount}`)
    console.log('From Email Server Backend. END -----')
    return {
        message: `Total of ${totalEmailCount} Emails triggered. Success: ${successEmailCount} Fail: ${failEmailCount}`
    };
}



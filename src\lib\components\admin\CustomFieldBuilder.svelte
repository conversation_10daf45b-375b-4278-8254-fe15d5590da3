<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Switch } from '$lib/components/ui/switch';

	import {
		Dialog,
		DialogContent,
		DialogDescription,
		DialogFooter,
		DialogHeader,
		DialogTitle
	} from '$lib/components/ui/dialog';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Plus, X } from 'lucide-svelte';
	import type { FormFieldType, FieldOptions, ValidationRules } from '$lib/types/formFields';
	import { FIELD_TYPE_CONFIGS } from '$lib/types/formFields';

	// Props
	export let eventId: string;
	export let onSave: (fieldData: any) => void;
	export let onCancel: () => void;

	// Form state
	let fieldLabel = '';
	let fieldType: FormFieldType = 'text';
	let isRequired = false;
	let placeholderText = '';
	let helpText = '';
	let displayOrder = 0;

	// Field options (for select/checkbox)
	let fieldOptions: string[] = [''];
	let allowMultiple = false;

	// Validation rules
	let minLength: number | undefined;
	let maxLength: number | undefined;
	let minValue: number | undefined;
	let maxValue: number | undefined;
	let pattern = '';
	let customMessage = '';

	// State
	let saving = false;
	let errors: Record<string, string> = {};

	// Reactive values
	$: fieldKey = generateFieldKey(fieldLabel);
	$: hasOptions = FIELD_TYPE_CONFIGS[fieldType]?.hasOptions || false;
	$: isValid = fieldLabel.trim() !== '' && fieldKey !== '';

	function generateFieldKey(label: string): string {
		return label
			.toLowerCase()
			.replace(/[^a-z0-9\s]/g, '')
			.replace(/\s+/g, '_')
			.substring(0, 50);
	}

	function addOption() {
		fieldOptions = [...fieldOptions, ''];
	}

	function removeOption(index: number) {
		fieldOptions = fieldOptions.filter((_, i) => i !== index);
	}

	function updateOption(index: number, value: string) {
		fieldOptions[index] = value;
		fieldOptions = [...fieldOptions];
	}

	async function handleSave() {
		if (!isValid) return;

		saving = true;
		errors = {};

		try {
			// Prepare field options
			let options: FieldOptions | undefined;
			if (hasOptions) {
				const validOptions = fieldOptions.filter((opt) => opt.trim() !== '');
				if (validOptions.length > 0) {
					options = {
						options: validOptions,
						multiple: fieldType === 'checkbox' ? allowMultiple : false
					};
				}
			}

			// Prepare validation rules
			let validationRules: ValidationRules | undefined;
			const rules: ValidationRules = {};

			if (minLength !== undefined && minLength > 0) rules.minLength = minLength;
			if (maxLength !== undefined && maxLength > 0) rules.maxLength = maxLength;
			if (minValue !== undefined) rules.min = minValue;
			if (maxValue !== undefined) rules.max = maxValue;
			if (pattern.trim() !== '') rules.pattern = pattern.trim();
			if (customMessage.trim() !== '') rules.customMessage = customMessage.trim();

			if (Object.keys(rules).length > 0) {
				validationRules = rules;
			}

			// Prepare field data
			const fieldData = {
				eventId,
				fieldKey,
				fieldLabel: fieldLabel.trim(),
				fieldType,
				fieldOptions: options,
				isRequired,
				isStandardField: false,
				isEnabled: true,
				displayOrder,
				validationRules,
				placeholderText: placeholderText.trim() || null,
				helpText: helpText.trim() || null
			};

			onSave(fieldData);
		} catch (error) {
			console.error('Error saving field:', error);
			errors.general = 'Failed to save field. Please try again.';
		} finally {
			saving = false;
		}
	}

	function handleCancel() {
		onCancel();
	}
</script>

<Dialog open={true} onOpenChange={handleCancel}>
	<DialogContent class="max-h-[90vh] max-w-2xl overflow-y-auto">
		<DialogHeader>
			<DialogTitle>Create Custom Field</DialogTitle>
			<DialogDescription>Add a new custom field to your event registration form.</DialogDescription>
		</DialogHeader>

		<div class="space-y-6">
			<!-- Basic Information -->
			<Card>
				<CardHeader>
					<CardTitle class="text-lg">Basic Information</CardTitle>
				</CardHeader>
				<CardContent class="space-y-4">
					<!-- Field Label -->
					<div class="space-y-2">
						<Label for="fieldLabel">Field Label *</Label>
						<Input
							id="fieldLabel"
							bind:value={fieldLabel}
							placeholder="Enter field label (e.g., 'Dietary Requirements')"
							class={errors.fieldLabel ? 'border-red-500' : ''}
						/>
						{#if errors.fieldLabel}
							<p class="text-sm text-red-500">{errors.fieldLabel}</p>
						{/if}
						{#if fieldKey}
							<p class="text-xs text-muted-foreground">
								Field key: <code class="rounded bg-muted px-1">{fieldKey}</code>
							</p>
						{/if}
					</div>

					<!-- Field Type -->
					<div class="space-y-2">
						<Label for="fieldType">Field Type *</Label>
						<select
							id="fieldType"
							bind:value={fieldType}
							class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
						>
							{#each Object.entries(FIELD_TYPE_CONFIGS) as [type, config]}
								<option value={type}>{config.label} - {config.description}</option>
							{/each}
						</select>
					</div>

					<!-- Required Toggle -->
					<div class="flex items-center justify-between">
						<div class="space-y-0.5">
							<Label>Required Field</Label>
							<p class="text-sm text-muted-foreground">
								Users must fill this field to submit the form
							</p>
						</div>
						<Switch bind:checked={isRequired} />
					</div>
				</CardContent>
			</Card>

			<!-- Field Options (for select/checkbox) -->
			{#if hasOptions}
				<Card>
					<CardHeader>
						<CardTitle class="text-lg">Field Options</CardTitle>
						<CardDescription>Define the available options for this field.</CardDescription>
					</CardHeader>
					<CardContent class="space-y-4">
						{#if fieldType === 'checkbox'}
							<div class="flex items-center justify-between">
								<div class="space-y-0.5">
									<Label>Allow Multiple Selections</Label>
									<p class="text-sm text-muted-foreground">
										Allow users to select multiple options
									</p>
								</div>
								<Switch bind:checked={allowMultiple} />
							</div>
						{/if}

						<div class="space-y-2">
							<Label>Options</Label>
							{#each fieldOptions as option, index}
								<div class="flex gap-2">
									<Input
										bind:value={option}
										placeholder="Enter option {index + 1}"
										oninput={(e: Event) =>
											updateOption(index, (e.target as HTMLInputElement).value)}
									/>
									{#if fieldOptions.length > 1}
										<Button variant="outline" size="sm" onclick={() => removeOption(index)}>
											<X class="h-4 w-4" />
										</Button>
									{/if}
								</div>
							{/each}
							<Button variant="outline" onclick={addOption} class="w-full">
								<Plus class="mr-2 h-4 w-4" />
								Add Option
							</Button>
						</div>
					</CardContent>
				</Card>
			{/if}

			<!-- Validation Rules -->
			<Card>
				<CardHeader>
					<CardTitle class="text-lg">Validation Rules</CardTitle>
					<CardDescription>Set validation constraints for this field.</CardDescription>
				</CardHeader>
				<CardContent class="space-y-4">
					{#if fieldType === 'text' || fieldType === 'textarea' || fieldType === 'email'}
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<Label for="minLength">Minimum Length</Label>
								<Input
									id="minLength"
									type="number"
									bind:value={minLength}
									placeholder="0"
									min="0"
								/>
							</div>
							<div class="space-y-2">
								<Label for="maxLength">Maximum Length</Label>
								<Input
									id="maxLength"
									type="number"
									bind:value={maxLength}
									placeholder="255"
									min="1"
								/>
							</div>
						</div>
					{/if}

					{#if fieldType === 'number'}
						<div class="grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<Label for="minValue">Minimum Value</Label>
								<Input id="minValue" type="number" bind:value={minValue} placeholder="0" />
							</div>
							<div class="space-y-2">
								<Label for="maxValue">Maximum Value</Label>
								<Input id="maxValue" type="number" bind:value={maxValue} placeholder="100" />
							</div>
						</div>
					{/if}

					<div class="space-y-2">
						<Label for="pattern">Custom Pattern (Regex)</Label>
						<Input id="pattern" bind:value={pattern} placeholder="^[A-Za-z]+$" />
						<p class="text-xs text-muted-foreground">
							Optional regular expression pattern for validation
						</p>
					</div>

					<div class="space-y-2">
						<Label for="customMessage">Custom Error Message</Label>
						<Input
							id="customMessage"
							bind:value={customMessage}
							placeholder="Please enter a valid value"
						/>
					</div>
				</CardContent>
			</Card>

			<!-- Additional Settings -->
			<Card>
				<CardHeader>
					<CardTitle class="text-lg">Additional Settings</CardTitle>
				</CardHeader>
				<CardContent class="space-y-4">
					<div class="space-y-2">
						<Label for="placeholderText">Placeholder Text</Label>
						<Input
							id="placeholderText"
							bind:value={placeholderText}
							placeholder="Enter placeholder text..."
						/>
					</div>

					<div class="space-y-2">
						<Label for="helpText">Help Text</Label>
						<Textarea
							id="helpText"
							bind:value={helpText}
							placeholder="Additional instructions or help text for users..."
							rows={2}
						/>
					</div>

					<div class="space-y-2">
						<Label for="displayOrder">Display Order</Label>
						<Input
							id="displayOrder"
							type="number"
							bind:value={displayOrder}
							placeholder="0"
							min="0"
						/>
						<p class="text-xs text-muted-foreground">Lower numbers appear first in the form</p>
					</div>
				</CardContent>
			</Card>

			{#if errors.general}
				<div class="rounded bg-red-50 p-3 text-sm text-red-500">
					{errors.general}
				</div>
			{/if}
		</div>

		<DialogFooter>
			<Button variant="outline" onclick={handleCancel} disabled={saving}>Cancel</Button>
			<Button onclick={handleSave} disabled={!isValid || saving}>
				{#if saving}
					<div class="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
				{/if}
				Create Field
			</Button>
		</DialogFooter>
	</DialogContent>
</Dialog>

<style>
	code {
		font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
		font-size: 0.85em;
	}
</style>

<script lang="ts">
	import { Globe } from 'lucide-svelte';
	import { page } from '$app/stores';
	import { onMount } from 'svelte';
	import { formatDate, formatTime } from '$lib/utils/formatdatetime.js';
	import Footer from '$lib/components/Footer.svelte';
	import Markdown from '$lib/components/Markdown.svelte';
	import DynamicRegistrationForm from '$lib/components/forms/DynamicRegistrationForm.svelte';
	import { analytics } from '$lib/analytics/analytics';

	export let data;

	$: isPreview = $page.url.searchParams.has('preview');

	// Track event page view when component mounts
	onMount(() => {
		if (data.event) {
			analytics.trackEventPageView(data.event.id, $page.params.slug, data.event.title);

			// Track event publishing if coming from publish action
			if ($page.url.searchParams.get('published') === 'true') {
				analytics.trackEventPublished(data.event.id);
			}
		}
	});

	// Track location link click
	function handleLocationClick() {
		if (data.event) {
			analytics.trackLinkClick('View Map', data.event.location_url || '', 'event_page_location');
		}
	}

	// Define your page metadata here
	const pageTitle = `${data.event?.title} | eHaris Events`;
	const pageDescription = `Register for ${data.event?.title}`;
	const siteName = 'eventsdemo.eHaris.com';

	const title = `${data.event?.title}`;
	const description = `${data.event?.short_description}`;
	const image = data.event?.image_url;
</script>

<svelte:head>
	<title>{pageTitle}</title>
	<meta name="description" content={pageDescription} />

	<!-- Open Graph / Facebook -->
	<meta property="og:type" content="website" />
	<meta property="og:url" content={$page.url.href} />
	<meta property="og:title" content={title} />
	<meta property="og:description" content={description} />
	<meta property="og:image" content={image} />

	<!-- Twitter -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:url" content={$page.url.href} />
	<meta name="twitter:title" content={title} />
	<meta name="twitter:description" content={description} />
	<meta name="twitter:image" content={image} />

	<!-- WhatsApp -->
	<meta property="og:site_name" content={siteName} />
	<meta property="og:locale" content="en_US" />
	<meta property="og:image:width" content="1200" />
	<meta property="og:image:height" content="630" />
</svelte:head>

<main class="mx-auto max-w-4xl px-4 py-8">
	<div class="mb-8">
		<img
			src={data.event?.image_url}
			alt={data.event?.title}
			class="h-auto w-full rounded-lg object-cover shadow-lg"
		/>
	</div>

	<h1 class="mb-4 text-3xl font-bold">{data.event?.title}</h1>

	<div class="mb-6 text-gray-600">
		<p class="mb-2">
			<strong>Date:</strong>
			{formatDate(data.event?.start_date)} - {formatDate(data.event?.end_date)}
		</p>
		<p class=" mb-2 flex">
			<strong>Time:</strong>
			<span class="ml-1"
				>{formatTime(data.event?.start_time)} - {formatTime(data.event?.end_time)}</span
			> <span class="ml-3 flex items-center"><Globe size="15" /> +3:00 GMT</span>
		</p>
		<p>
			<strong>Location:</strong>
			{data.event?.location} -
			<a
				class="text-blue-500"
				href={data.event?.location_url}
				target="_blank"
				onclick={handleLocationClick}
			>
				View Map
			</a>
		</p>
	</div>

	<h2 class="mb-4 text-2xl font-semibold">About the Event</h2>

	<!-- <p class="mb-6 text-justify text-gray-700">{data.event?.long_description}</p> -->
	<!-- <div class="event-description">
		<Markdown content={data.event?.long_description} />
	</div> -->
	<!-- English description with LTR -->
	<div class="event-description mb-6">
		<Markdown content={data.event?.long_description} />
	</div>
	<br />
	<!-- <p class="mb-6 text-justify text-gray-700">{data.event?.long_description_arabic}</p> -->
	<!-- <div class="event-description">
		<Markdown content={data.event?.long_description_arabic} />
	</div> -->
	<!-- Arabic description with RTL -->
	{#if data.event?.long_description_arabic}
		<div class="event-description mb-6">
			<Markdown content={data.event?.long_description_arabic} isRTL={true} />
		</div>
	{/if}

	<!-- Registration Status Messages -->
	{#if data.registrationStatus && !data.registrationStatus.isOpen}
		<div class="flex justify-between rounded bg-red-400 px-8 py-6 text-white">
			<div class="flex items-center">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="mr-6 h-7 w-7"
					viewBox="0 0 20 20"
					fill="currentColor"
				>
					<path
						fill-rule="evenodd"
						d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
						clip-rule="evenodd"
					/>
				</svg>
				<p>{data.registrationStatus.reason}</p>
			</div>
		</div>
	{:else if data.waitingList}
		<!-- Warning for full capacity -->
		<div class="flex justify-between rounded bg-yellow-400 px-8 py-6 text-black">
			<div class="flex items-center">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="mr-6 h-7 w-7"
					viewBox="0 0 20 20"
					fill="currentColor"
				>
					<path
						d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"
					/></svg
				>
				<p>Registration is Full. Waiting List is enabled</p>
			</div>
		</div>
	{/if}

	<!-- Form  -->
	<div>
		<!-- Dynamic Form (now handles all registration forms) -->
		<DynamicRegistrationForm
			eventId={data.event?.id}
			eventSlug={$page.params.slug}
			formFields={data.formFields}
			registrationStatus={data.registrationStatus}
			preview={isPreview}
		/>
	</div>
	<Footer />
</main>

# PostHog Integration for SvelteKit

This document provides comprehensive documentation for the PostHog analytics integration in your SvelteKit application.

## Features

✅ **Product Analytics** - Event tracking, user behavior analysis  
✅ **Web Analytics** - Page views, user sessions  
✅ **Session Replay** - Recording user interactions  
✅ **Error Tracking** - Capturing and monitoring errors  
✅ **Feature Flags** - A/B testing and feature toggles  
✅ **Privacy Controls** - GDPR compliance and user consent  
✅ **TypeScript Support** - Full type safety  

## Quick Start

### 1. Environment Setup

Create a `.env` file in your project root:

```env
PUBLIC_POSTHOG_KEY=phc_kYV0tnmpapd7giA8sKKMs3YqyZ55sJMHyMlnI7Tr9zl
PUBLIC_POSTHOG_HOST=https://app.posthog.com
PUBLIC_POSTHOG_DEBUG=false
PUBLIC_POSTHOG_DISABLED=false
```

### 2. Basic Usage

The integration is automatically initialized in your app layout. To track events:

```typescript
import { trackEvent, trackPageView, identifyUser } from '$lib/posthog';

// Track custom events
trackEvent('button_clicked', {
  button_name: 'signup',
  page: 'landing'
});

// Track page views
trackPageView('Dashboard', {
  user_type: 'premium'
});

// Identify users
identifyUser('user123', {
  email: '<EMAIL>',
  name: 'John Doe'
});
```

## API Reference

### Event Tracking

#### `trackEvent(eventName, properties?, options?)`

Track custom events with optional properties.

```typescript
trackEvent('product_viewed', {
  product_id: 'abc123',
  product_name: 'Widget',
  price: 29.99,
  category: 'electronics'
});
```

#### `trackPageView(pageName?, properties?)`

Track page views with optional metadata.

```typescript
trackPageView('Product Details', {
  product_category: 'electronics',
  user_segment: 'premium'
});
```

#### `trackButtonClick(buttonName, properties?)`

Track button interactions.

```typescript
trackButtonClick('add_to_cart', {
  product_id: 'abc123',
  source: 'product_page'
});
```

#### `trackFormSubmit(formName, properties?)`

Track form submissions.

```typescript
trackFormSubmit('contact_form', {
  form_type: 'lead_generation',
  field_count: 5
});
```

### User Management

#### `identifyUser(userId, properties?)`

Identify a user with optional properties.

```typescript
identifyUser('user123', {
  email: '<EMAIL>',
  name: 'John Doe',
  plan: 'premium',
  signup_date: '2024-01-15'
});
```

#### `setUserProperties(properties)`

Update user properties.

```typescript
setUserProperties({
  last_login: new Date().toISOString(),
  feature_usage_count: 42
});
```

#### `logoutUser()`

Clear user identification.

```typescript
logoutUser();
```

### Feature Flags

#### `getFeatureFlag(flagName)`

Get a feature flag value.

```typescript
const newDesign = getFeatureFlag('new_design_enabled');
if (newDesign) {
  // Show new design
}
```

#### `isFeatureEnabled(flagName)`

Check if a feature is enabled (boolean flags).

```typescript
if (isFeatureEnabled('beta_features')) {
  // Show beta features
}
```

#### `getAllFeatureFlags()`

Get all feature flags.

```typescript
const flags = getAllFeatureFlags();
console.log('All flags:', flags);
```

### Error Tracking

#### `trackError(error, context?)`

Track JavaScript errors.

```typescript
try {
  // Some operation
} catch (error) {
  trackError(error, {
    operation: 'data_processing',
    user_action: 'form_submit'
  });
}
```

#### `withErrorTracking(asyncFunction, context?)`

Wrap async functions with automatic error tracking.

```typescript
const safeApiCall = withErrorTracking(async () => {
  const response = await fetch('/api/data');
  return response.json();
}, 'api_data_fetch');
```

### Session Replay

#### `startSessionReplay()`

Start session recording.

```typescript
startSessionReplay();
```

#### `stopSessionReplay()`

Stop session recording.

```typescript
stopSessionReplay();
```

### Privacy Controls

#### `optOutOfTracking()`

Opt user out of tracking.

```typescript
optOutOfTracking();
```

#### `optInToTracking()`

Opt user into tracking.

```typescript
optInToTracking();
```

#### `hasOptedOut()`

Check if user has opted out.

```typescript
if (hasOptedOut()) {
  // Show privacy notice
}
```

## Components

### ErrorBoundary

Wrap components to catch and track errors automatically.

```svelte
<script>
  import { ErrorBoundary } from '$lib/posthog';
</script>

<ErrorBoundary componentName="UserDashboard">
  <UserDashboard />
</ErrorBoundary>
```

### Custom Error Fallback

```svelte
<ErrorBoundary componentName="MyComponent">
  <MyComponent />
  
  {#snippet fallback({ error, resetError })}
    <div class="error-state">
      <h2>Something went wrong</h2>
      <p>{error.message}</p>
      <button onclick={resetError}>Try Again</button>
    </div>
  {/snippet}
</ErrorBoundary>
```

## Stores

Access reactive PostHog state using Svelte stores.

```svelte
<script>
  import { 
    posthogAvailable, 
    currentUser, 
    sessionReplayEnabled,
    featureFlags 
  } from '$lib/posthog/stores';
</script>

{#if $posthogAvailable}
  <p>PostHog is ready!</p>
{/if}

{#if $currentUser}
  <p>Welcome, {$currentUser.name}!</p>
{/if}

<p>Session replay: {$sessionReplayEnabled ? 'On' : 'Off'}</p>

{#if $featureFlags.new_feature}
  <NewFeatureComponent />
{/if}
```

## Testing

### Test Your Integration

Visit `/posthog-test` to run comprehensive integration tests.

### Programmatic Testing

```typescript
import { runPostHogTests } from '$lib/posthog/test-utils';

// Run all tests
const results = await runPostHogTests();
console.log('Test results:', results);
```

## Best Practices

### 1. Event Naming

Use consistent, descriptive event names:

```typescript
// Good
trackEvent('product_added_to_cart', { product_id: 'abc123' });

// Avoid
trackEvent('click', { thing: 'button' });
```

### 2. Property Structure

Keep properties consistent and meaningful:

```typescript
trackEvent('page_view', {
  page_name: 'Product Details',
  page_category: 'ecommerce',
  user_type: 'authenticated',
  timestamp: new Date().toISOString()
});
```

### 3. Error Context

Provide useful context when tracking errors:

```typescript
trackError(error, {
  component: 'CheckoutForm',
  user_action: 'submit_payment',
  form_data: { payment_method: 'card' },
  user_id: currentUserId
});
```

### 4. Privacy Compliance

Always respect user privacy preferences:

```typescript
// Check consent before tracking
if (!hasOptedOut()) {
  trackEvent('user_action', properties);
}
```

### 5. Performance

Batch events when possible and avoid tracking in tight loops:

```typescript
// Good - track meaningful user actions
button.onclick = () => trackButtonClick('save_document');

// Avoid - tracking every keystroke
input.oninput = () => trackEvent('keystroke'); // Don't do this
```

## Troubleshooting

### Common Issues

1. **PostHog not loading**
   - Check your API key in `.env`
   - Verify network connectivity
   - Check browser console for errors

2. **Events not appearing**
   - Ensure PostHog is initialized (`posthogAvailable` store)
   - Check if user has opted out
   - Verify event properties are serializable

3. **Session replay not working**
   - Check browser compatibility
   - Verify session recording is enabled in PostHog project
   - Check for privacy settings blocking recording

4. **Feature flags not updating**
   - Ensure user is identified
   - Check flag configuration in PostHog dashboard
   - Verify network connectivity

### Debug Mode

Enable debug mode for development:

```env
PUBLIC_POSTHOG_DEBUG=true
```

Or programmatically:

```typescript
import { debugPostHog } from '$lib/posthog';
debugPostHog(); // Logs current PostHog state
```

## Advanced Usage

### Custom Event Properties

Create typed event properties for better type safety:

```typescript
interface ProductViewProperties extends PostHogEventProperties {
  product_id: string;
  product_name: string;
  product_category: string;
  product_price: number;
}

trackEvent('product_viewed', {
  product_id: 'abc123',
  product_name: 'Widget',
  product_category: 'electronics',
  product_price: 29.99
} as ProductViewProperties);
```

### Group Analytics

Track group-level analytics:

```typescript
import { identifyGroup } from '$lib/posthog';

identifyGroup('company', 'acme-corp', {
  name: 'Acme Corporation',
  plan: 'enterprise',
  employee_count: 500
});
```

### A/B Testing

Use feature flags for A/B testing:

```typescript
const variant = getVariant('checkout_flow_test');

if (variant === 'variant_a') {
  // Show original checkout
} else if (variant === 'variant_b') {
  // Show new checkout
}
```

## Support

For issues with this integration, check:

1. Browser console for error messages
2. PostHog dashboard for event delivery
3. Network tab for failed requests
4. `/posthog-test` page for integration status

For PostHog-specific issues, visit [PostHog Documentation](https://posthog.com/docs).

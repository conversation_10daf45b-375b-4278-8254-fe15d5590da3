// Dynamic validation utilities for flexible forms

import { z } from 'zod';
import type { FormField, DynamicFormData, ValidationRules } from '$lib/types/formFields';

/**
 * Generate a Zod schema dynamically based on form fields
 */
export function generateDynamicSchema(formFields: FormField[]): z.ZodSchema<DynamicFormData> {
  const schemaFields: Record<string, z.ZodTypeAny> = {};

  for (const field of formFields) {
    if (!field.isEnabled) continue;

    let fieldSchema: z.ZodTypeAny;

    // Base schema based on field type
    switch (field.fieldType) {
      case 'text':
      case 'textarea':
        fieldSchema = z.string();
        break;

      case 'email':
        fieldSchema = z.string().email('Invalid email address');
        break;

      case 'phone':
        fieldSchema = z.string().regex(/^\+?[0-9]{8,14}$/, 'Invalid phone number');
        break;

      case 'url':
        fieldSchema = z.string().url('Invalid URL');
        break;

      case 'number':
        fieldSchema = z.coerce.number();
        break;

      case 'date':
        fieldSchema = z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format');
        break;

      case 'select':
        if (field.fieldOptions?.options && field.fieldOptions.options.length > 0) {
          fieldSchema = z.enum(field.fieldOptions.options as [string, ...string[]]);
        } else {
          fieldSchema = z.string();
        }
        break;

      case 'checkbox':
        if (field.fieldOptions?.multiple) {
          fieldSchema = z.array(z.string());
        } else {
          fieldSchema = z.boolean();
        }
        break;

      default:
        fieldSchema = z.string();
    }

    // Apply validation rules
    if (field.validationRules) {
      fieldSchema = applyValidationRules(fieldSchema, field.validationRules, field.fieldType);
    }

    // Handle required fields
    if (field.isRequired) {
      if (field.fieldType === 'checkbox' && !field.fieldOptions?.multiple) {
        fieldSchema = fieldSchema.refine(val => val === true, {
          message: `${field.fieldLabel} must be checked`
        });
      } else if (field.fieldType === 'checkbox' && field.fieldOptions?.multiple) {
        fieldSchema = (fieldSchema as z.ZodArray<any>).min(1, `At least one ${field.fieldLabel} must be selected`);
      } else {
        fieldSchema = fieldSchema.min(1, `${field.fieldLabel} is required`);
      }
    } else {
      // Make field optional
      if (field.fieldType === 'checkbox' && field.fieldOptions?.multiple) {
        fieldSchema = fieldSchema.optional().default([]);
      } else if (field.fieldType === 'checkbox') {
        fieldSchema = fieldSchema.optional().default(false);
      } else {
        fieldSchema = fieldSchema.optional();
      }
    }

    schemaFields[field.fieldKey] = fieldSchema;
  }

  return z.object(schemaFields);
}

/**
 * Apply validation rules to a Zod schema
 */
function applyValidationRules(schema: z.ZodTypeAny, rules: ValidationRules, fieldType: string): z.ZodTypeAny {
  let updatedSchema = schema;

  // String-based validations
  if (fieldType === 'text' || fieldType === 'textarea' || fieldType === 'email' || fieldType === 'phone' || fieldType === 'url') {
    if (rules.minLength !== undefined) {
      updatedSchema = (updatedSchema as z.ZodString).min(rules.minLength,
        rules.customMessage || `Must be at least ${rules.minLength} characters`);
    }

    if (rules.maxLength !== undefined) {
      updatedSchema = (updatedSchema as z.ZodString).max(rules.maxLength,
        rules.customMessage || `Must be no more than ${rules.maxLength} characters`);
    }

    if (rules.pattern) {
      updatedSchema = (updatedSchema as z.ZodString).regex(new RegExp(rules.pattern),
        rules.customMessage || 'Invalid format');
    }
  }

  // Number-based validations
  if (fieldType === 'number') {
    if (rules.min !== undefined) {
      updatedSchema = (updatedSchema as z.ZodNumber).min(rules.min,
        rules.customMessage || `Must be at least ${rules.min}`);
    }

    if (rules.max !== undefined) {
      updatedSchema = (updatedSchema as z.ZodNumber).max(rules.max,
        rules.customMessage || `Must be no more than ${rules.max}`);
    }
  }

  return updatedSchema;
}

/**
 * Validate form data using generated schema
 */
export function validateDynamicFormData(formFields: FormField[], formData: DynamicFormData): {
  success: boolean;
  data?: DynamicFormData;
  errors?: Record<string, string[]>;
} {
  try {
    const schema = generateDynamicSchema(formFields);
    const result = schema.safeParse(formData);

    if (result.success) {
      return { success: true, data: result.data };
    } else {
      const errors: Record<string, string[]> = {};

      result.error.errors.forEach(error => {
        const fieldKey = error.path.join('.');
        if (!errors[fieldKey]) {
          errors[fieldKey] = [];
        }
        errors[fieldKey].push(error.message);
      });

      return { success: false, errors };
    }
  } catch (error) {
    console.error('Error validating dynamic form data:', error);
    return {
      success: false,
      errors: { general: ['Validation failed due to server error'] }
    };
  }
}

/**
 * Generate client-side validation constraints for superforms
 */
export function generateFormConstraints(formFields: FormField[]): Record<string, any> {
  const constraints: Record<string, any> = {};

  for (const field of formFields) {
    if (!field.isEnabled) continue;

    const fieldConstraints: any = {};

    // Required constraint
    if (field.isRequired) {
      fieldConstraints.required = true;
    }

    // Type-specific constraints
    switch (field.fieldType) {
      case 'text':
      case 'textarea':
        if (field.validationRules?.minLength) {
          fieldConstraints.minlength = field.validationRules.minLength;
        }
        if (field.validationRules?.maxLength) {
          fieldConstraints.maxlength = field.validationRules.maxLength;
        }
        if (field.validationRules?.pattern) {
          fieldConstraints.pattern = field.validationRules.pattern;
        }
        break;

      case 'email':
        fieldConstraints.type = 'email';
        if (field.validationRules?.minLength) {
          fieldConstraints.minlength = field.validationRules.minLength;
        }
        if (field.validationRules?.maxLength) {
          fieldConstraints.maxlength = field.validationRules.maxLength;
        }
        if (field.validationRules?.pattern) {
          fieldConstraints.pattern = field.validationRules.pattern;
        }
        break;

      case 'phone':
        fieldConstraints.type = 'tel';
        if (field.validationRules?.minLength) {
          fieldConstraints.minlength = field.validationRules.minLength;
        }
        if (field.validationRules?.maxLength) {
          fieldConstraints.maxlength = field.validationRules.maxLength;
        }
        if (field.validationRules?.pattern) {
          fieldConstraints.pattern = field.validationRules.pattern;
        }
        break;

      case 'url':
        fieldConstraints.type = 'url';
        if (field.validationRules?.minLength) {
          fieldConstraints.minlength = field.validationRules.minLength;
        }
        if (field.validationRules?.maxLength) {
          fieldConstraints.maxlength = field.validationRules.maxLength;
        }
        if (field.validationRules?.pattern) {
          fieldConstraints.pattern = field.validationRules.pattern;
        }
        break;

      case 'number':
        fieldConstraints.type = 'number';
        if (field.validationRules?.min !== undefined) {
          fieldConstraints.min = field.validationRules.min;
        }
        if (field.validationRules?.max !== undefined) {
          fieldConstraints.max = field.validationRules.max;
        }
        break;

      case 'date':
        fieldConstraints.type = 'date';
        break;
    }

    constraints[field.fieldKey] = fieldConstraints;
  }

  return constraints;
}

/**
 * Convert form field validation rules to HTML5 validation attributes
 */
export function getFieldValidationAttributes(field: FormField): Record<string, any> {
  const attributes: Record<string, any> = {};

  if (field.isRequired) {
    attributes.required = true;
  }

  if (field.placeholderText) {
    attributes.placeholder = field.placeholderText;
  }

  switch (field.fieldType) {
    case 'email':
      attributes.type = 'email';
      break;
    case 'phone':
      attributes.type = 'tel';
      break;
    case 'url':
      attributes.type = 'url';
      break;
    case 'number':
      attributes.type = 'number';
      break;
    case 'date':
      attributes.type = 'date';
      break;
    default:
      attributes.type = 'text';
  }

  if (field.validationRules) {
    const rules = field.validationRules;

    if (rules.minLength !== undefined) {
      attributes.minlength = rules.minLength;
    }

    if (rules.maxLength !== undefined) {
      attributes.maxlength = rules.maxLength;
    }

    if (rules.pattern) {
      attributes.pattern = rules.pattern;
    }

    if (field.fieldType === 'number') {
      if (rules.min !== undefined) {
        attributes.min = rules.min;
      }
      if (rules.max !== undefined) {
        attributes.max = rules.max;
      }
    }
  }

  return attributes;
}

/**
 * Sanitize and prepare form data for storage
 */
export function sanitizeFormData(formData: DynamicFormData): DynamicFormData {
  const sanitized: DynamicFormData = {};

  for (const [key, value] of Object.entries(formData)) {
    if (value === null || value === undefined) {
      continue;
    }

    if (typeof value === 'string') {
      // Trim whitespace and sanitize
      sanitized[key] = value.trim();
    } else if (Array.isArray(value)) {
      // Filter out empty values and trim strings
      sanitized[key] = value
        .filter(v => v !== null && v !== undefined && v !== '')
        .map(v => typeof v === 'string' ? v.trim() : v);
    } else {
      sanitized[key] = value;
    }
  }

  return sanitized;
}

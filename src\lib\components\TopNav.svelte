<script>
	import { page } from '$app/stores';
	import { Menu, X, User, Settings, LogOut } from 'lucide-svelte';

	let isMenuOpen = false;

	const navItems = [
		{ href: '/dashboard', label: 'Dashboard' },
		{ href: '/projects', label: 'Projects' },
		{ href: '/tasks', label: 'Tasks' },
		{ href: '/reports', label: 'Reports' }
	];

	function toggleMenu() {
		isMenuOpen = !isMenuOpen;
	}
</script>

<nav class="bg-white shadow">
	<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
		<div class="flex h-16 justify-between">
			<div class="flex">
				<div class="flex flex-shrink-0 items-center">
					<!-- Replace with your logo -->
					<svg
						class="h-8 w-8 text-indigo-600"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M13 10V3L4 14h7v7l9-11h-7z"
						/>
					</svg>
					<span class="ml-2 text-xl font-bold text-gray-800">DashCo</span>
				</div>
				<div class="hidden sm:ml-6 sm:flex sm:space-x-8">
					{#each navItems as item}
						<a
							href={item.href}
							class="
                  {$page.url.pathname === item.href
								? 'border-indigo-500 text-gray-900'
								: 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'}
                  inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium
                "
						>
							{item.label}
						</a>
					{/each}
				</div>
			</div>
			<div class="hidden sm:ml-6 sm:flex sm:items-center">
				<div class="relative ml-3">
					<div>
						<button
							type="button"
							class="flex rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
							id="user-menu"
							aria-expanded="false"
							aria-haspopup="true"
						>
							<span class="sr-only">Open user menu</span>
							<img
								class="h-8 w-8 rounded-full"
								src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
								alt=""
							/>
						</button>
					</div>
					<!-- Dropdown menu, show/hide based on menu state -->
					<div
						class="absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
						role="menu"
						aria-orientation="vertical"
						aria-labelledby="user-menu"
					>
						<a
							href="/profile"
							class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
							role="menuitem"
						>
							<User class="mr-2 inline-block h-4 w-4" />
							Your Profile
						</a>
						<a
							href="/settings"
							class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
							role="menuitem"
						>
							<Settings class="mr-2 inline-block h-4 w-4" />
							Settings
						</a>
						<a
							href="/logout"
							class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
							role="menuitem"
						>
							<LogOut class="mr-2 inline-block h-4 w-4" />
							Sign out
						</a>
					</div>
				</div>
			</div>
			<div class="-mr-2 flex items-center sm:hidden">
				<!-- Mobile menu button -->
				<button
					type="button"
					class="inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500"
					aria-controls="mobile-menu"
					aria-expanded="false"
					on:click={toggleMenu}
				>
					<span class="sr-only">Open main menu</span>
					{#if isMenuOpen}
						<X class="block h-6 w-6" />
					{:else}
						<Menu class="block h-6 w-6" />
					{/if}
				</button>
			</div>
		</div>
	</div>

	<!-- Mobile menu, show/hide based on menu state -->
	{#if isMenuOpen}
		<div class="sm:hidden" id="mobile-menu">
			<div class="space-y-1 pb-3 pt-2">
				{#each navItems as item}
					<a
						href={item.href}
						class="
                {$page.url.pathname === item.href
							? 'border-indigo-500 bg-indigo-50 text-indigo-700'
							: 'border-transparent text-gray-500 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-700'}
                block border-l-4 py-2 pl-3 pr-4 text-base font-medium
              "
					>
						{item.label}
					</a>
				{/each}
			</div>
			<div class="border-t border-gray-200 pb-3 pt-4">
				<div class="flex items-center px-4">
					<div class="flex-shrink-0">
						<img
							class="h-10 w-10 rounded-full"
							src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
							alt=""
						/>
					</div>
					<div class="ml-3">
						<div class="text-base font-medium text-gray-800">Tom Cook</div>
						<div class="text-sm font-medium text-gray-500"><EMAIL></div>
					</div>
				</div>
				<div class="mt-3 space-y-1">
					<a
						href="/profile"
						class="block px-4 py-2 text-base font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-800"
					>
						Your Profile
					</a>
					<a
						href="/settings"
						class="block px-4 py-2 text-base font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-800"
					>
						Settings
					</a>
					<a
						href="/logout"
						class="block px-4 py-2 text-base font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-800"
					>
						Sign out
					</a>
				</div>
			</div>
		</div>
	{/if}
</nav>

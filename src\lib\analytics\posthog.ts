import { browser } from '$app/environment';
import { PUBLIC_POSTHOG_API_KEY, PUBLIC_POSTHOG_HOST } from '$env/static/public';
import posthog from 'posthog-js';

// PostHog configuration
const POSTHOG_CONFIG = {
	api_host: PUBLIC_POSTHOG_HOST || 'https://us.i.posthog.com',
	person_profiles: 'identified_only',
	loaded: (posthog: any) => {
		if (import.meta.env.DEV) console.log('PostHog loaded');
	},
	capture_pageview: false, // We'll handle page views manually
	capture_pageleave: true,
	// Enable the features we want
	autocapture: false, // We'll do manual tracking for better control
	session_recording: {
		enabled: true,
		maskAllInputs: true, // Mask sensitive inputs for privacy
		maskInputOptions: {
			password: true,
			email: false // We can track email field interactions
		}
	},
	// Error tracking
	capture_performance: true,
	// Privacy settings
	respect_dnt: true,
	opt_out_capturing_by_default: false,
	// Disable automatic properties we don't need
	property_blacklist: ['$performance_raw']
};

// Initialize PostHog
export function initPostHog(): void {
	if (browser && PUBLIC_POSTHOG_API_KEY) {
		posthog.init(PUBLIC_POSTHOG_API_KEY, POSTHOG_CONFIG);
		
		// Set up error tracking
		if (typeof window !== 'undefined') {
			window.addEventListener('error', (event) => {
				posthog.capture('javascript_error', {
					error_message: event.message,
					error_filename: event.filename,
					error_lineno: event.lineno,
					error_colno: event.colno,
					error_stack: event.error?.stack
				});
			});

			window.addEventListener('unhandledrejection', (event) => {
				posthog.capture('unhandled_promise_rejection', {
					error_message: event.reason?.message || 'Unhandled promise rejection',
					error_stack: event.reason?.stack
				});
			});
		}
	}
}

// Export PostHog instance
export { posthog };

// Helper function to check if PostHog is available
export function isPostHogAvailable(): boolean {
	return browser && !!PUBLIC_POSTHOG_API_KEY && posthog.__loaded;
}

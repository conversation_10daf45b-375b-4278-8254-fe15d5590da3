import type { PageServerLoad, Actions } from './$types';
import { error, redirect } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ params, locals: { supabase } }) => {
  const { eventid } = params;

  // Get event data
  const { data: event, error: eventError } = await supabase
    .from('events')
    .select('id, title, created_by, is_published')
    .eq('id', eventid)
    .single();

  if (eventError || !event) {
    throw error(404, 'Event not found');
  }

  return {
    event
  };
};

export const actions: Actions = {
  publishEvent: async ({ params, locals: { supabase, safeGetSession } }) => {
    const { session } = await safeGetSession();
    if (!session) {
      redirect(303, '/login');
    }

    const { eventid } = params;

    // Publish the event
    const { error: updateError } = await supabase
      .from('events')
      .update({ is_published: true })
      .eq('id', eventid);

    if (updateError) {
      console.error('Error publishing event:', updateError);
      throw error(500, 'Failed to publish event');
    }

    // Redirect to the published event page
    const { data: eventData } = await supabase
      .from('events')
      .select('title_slug')
      .eq('id', eventid)
      .single();

    if (eventData?.title_slug) {
      redirect(303, `/${eventData.title_slug}?published=true`);
    } else {
      redirect(303, `/private/events`);
    }
  }
};

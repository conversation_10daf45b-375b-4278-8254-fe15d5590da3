import { sendEmails } from '$lib/server/importEmail';
import type { Actions } from '@sveltejs/kit';
import { fail, redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals: { safeGetSession } }) => {
    const { session } = await safeGetSession();
    if (!session) {
        redirect(303, '/');
    }

};

export const actions: Actions = {
    sendEmails: async ({ request, params }) => {
        const data = await request.json();

        // console.log('Data CSV page:', data)

        if (!data || !Array.isArray(data)) {
            console.error('Invalid data format recived from Page.')
            return fail(400, { message: 'Invalid data format' });
        }

        if (data.length === 0) {
            console.error('Invalid data format recived from Page.')
            return fail(400, { message: 'No data provided' });
        }

        console.info('Preparing to send emails')

        const result = await sendEmails(data, params);

        console.log('Response form the Server Function to page server: ', result)


        // if (!result.success) {
        //     console.error('Error from send email server: ', result.message)
        //     return fail(500, { message: result.message });
        // }

        return { success: true, message: result.message };
    }
};
<script lang="ts" generics="TData, TValue">
	import {
		type ColumnDef,
		type PaginationState,
		type ColumnFiltersState,
		getCoreRowModel,
		getPaginationRowModel,
		getFilteredRowModel
	} from '@tanstack/table-core';
	import { createSvelteTable, FlexRender } from '$lib/components/ui/data-table/index.js';
	import * as Table from '$lib/components/ui/table/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Search } from 'lucide-svelte';
	import { QrCode, X } from 'lucide-svelte';

	import QrcodeStream from '$lib/components/qrcode-stream.svelte';
	import type { DetectedBarcode } from 'barcode-detector/pure';

	// Add QR scanner state
	let dialogOpen = $state(false);
	let scanError: string | null = $state(null);
	let paused = $state(false);

	function onDetect(detectedCodes: DetectedBarcode[]) {
		if (detectedCodes.length > 0) {
			// Update the global filter with the scanned QR code value
			table.setGlobalFilter(detectedCodes[0].rawValue);
			dialogOpen = false;
		}
	}

	function onError(error: Error) {
		console.error('Error:', error);
		scanError = error.message;
	}

	function handleDialogClose() {
		if (dialogOpen) {
			dialogOpen = false;
		}
	}

	function handleModalClick(e: Event) {
		console.log('Modal clicked');
		e.stopPropagation();
	}

	// Table config starts here

	type DataTableProps<TData, TValue> = {
		columns: ColumnDef<TData, TValue>[];
		dataa: TData[];
	};

	let { dataa, columns }: DataTableProps<TData, TValue> = $props();
	let pagination = $state<PaginationState>({ pageIndex: 0, pageSize: 10 });
	let columnFilters = $state<ColumnFiltersState>([]);
	let globalFilter = $state<string | undefined>(undefined);

	const table = createSvelteTable({
		get data() {
			return dataa;
		},
		columns,
		state: {
			get pagination() {
				return pagination;
			},
			get columnFilters() {
				return columnFilters;
			},
			get globalFilter() {
				return globalFilter;
			}
		},
		onPaginationChange: (updater) => {
			if (typeof updater === 'function') {
				pagination = updater(pagination);
			} else {
				pagination = updater;
			}
		},
		onColumnFiltersChange: (updater) => {
			if (typeof updater === 'function') {
				columnFilters = updater(columnFilters);
			} else {
				columnFilters = updater;
			}
		},
		onGlobalFilterChange: (updater) => {
			if (typeof updater === 'function') {
				globalFilter = updater(globalFilter);
			} else {
				globalFilter = updater;
			}
		},
		getCoreRowModel: getCoreRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getFilteredRowModel: getFilteredRowModel()
	});

	function handleSearch(event: Event) {
		const target = event.target as HTMLInputElement;
		table.setGlobalFilter(target.value);
	}
</script>

<div>
	<!-- Old Search bar -->
	<!-- <div class="flex items-center space-x-4 py-4">
		<div class="relative w-full max-w-sm">
			<Input
				placeholder="Search all columns..."
				value={globalFilter ?? ''}
				oninput={handleSearch}
				class="pr-10"
			/>
			<div class="absolute right-3 top-1/2 -translate-y-1/2">
				<Search class="h-4 w-4 text-gray-500" />
			</div>
		</div>
	</div> -->

	<div class="flex items-center space-x-4 py-4">
		<div class="relative w-full max-w-sm">
			<Input
				placeholder="Search all columns..."
				value={globalFilter ?? ''}
				oninput={handleSearch}
				class="w-full rounded-md border border-gray-300 px-4 py-2 pr-10 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
			/>
			<!-- onclick={() => (dialogOpen = true)} -->
			<button
				type="button"
				class="absolute right-0 top-0 h-full px-3 text-gray-500 hover:text-gray-900"
				onclick={() => {
					if (globalFilter) {
						table.setGlobalFilter('');
					} else {
						dialogOpen = true;
					}
				}}
			>
				{#if globalFilter}
					<X class="h-5 w-5" />
				{:else}
					<QrCode class="h-5 w-5" />
				{/if}
				<span class="sr-only">{globalFilter ? 'Clear search' : 'Scan QR Code'}</span>
			</button>
		</div>
	</div>

	{#if dialogOpen}
		<div
			class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
			onclick={handleDialogClose}
		>
			<div
				class="w-full max-w-md overflow-hidden rounded-lg bg-white shadow-lg"
				onclick={handleModalClick}
			>
				<div class="relative">
					<button
						type="button"
						class="absolute right-2 top-2 z-10 rounded-full bg-white/80 p-1 backdrop-blur-sm"
						onclick={() => (dialogOpen = false)}
					>
						<X class="h-4 w-4" />
						<span class="sr-only">Close</span>
					</button>

					<div class="relative h-[300px] w-full overflow-hidden rounded-t-lg bg-black">
						{#if scanError}
							<p class="p-4 text-center text-white">{scanError}</p>
						{:else}
							<QrcodeStream
								{onDetect}
								{onError}
								{paused}
								constraints={{ facingMode: 'environment' }}
							/>
						{/if}
					</div>

					<div class="p-4 text-center">
						<p class="text-sm text-gray-500">Position the QR code within the camera view to scan</p>
					</div>
				</div>
			</div>
		</div>
	{/if}

	<div class="rounded-md border">
		<Table.Root>
			<Table.Header>
				{#each table.getHeaderGroups() as headerGroup (headerGroup.id)}
					<Table.Row>
						{#each headerGroup.headers as header (header.id)}
							<Table.Head>
								{#if !header.isPlaceholder}
									<FlexRender
										content={header.column.columnDef.header}
										context={header.getContext()}
									/>
								{/if}
							</Table.Head>
						{/each}
					</Table.Row>
				{/each}
			</Table.Header>
			<Table.Body>
				{#each table.getRowModel().rows as row (row.id)}
					<Table.Row data-state={row.getIsSelected() && 'selected'}>
						{#each row.getVisibleCells() as cell (cell.id)}
							<Table.Cell>
								<FlexRender content={cell.column.columnDef.cell} context={cell.getContext()} />
							</Table.Cell>
						{/each}
					</Table.Row>
				{:else}
					<Table.Row>
						<Table.Cell colspan={columns.length} class="h-24 text-center">No results.</Table.Cell>
					</Table.Row>
				{/each}
			</Table.Body>
		</Table.Root>
	</div>
	<div class="flex items-center justify-end space-x-2 py-4">
		<Button
			variant="outline"
			size="sm"
			onclick={() => table.previousPage()}
			disabled={!table.getCanPreviousPage()}
		>
			Previous
		</Button>
		<Button
			variant="outline"
			size="sm"
			onclick={() => table.nextPage()}
			disabled={!table.getCanNextPage()}
		>
			Next
		</Button>
	</div>
</div>

import { supabaseAdmin } from '$lib/server/db/supabaseAdmin'
import QRCode from 'qrcode'


export const generateQR = async (data, salt) => {

    // console.log('Data recived in the Generate QR Finction: dat, salt:', data, salt)

    if (!data) {
        console.error('No data provided')
        // return json({ error: 'No data provided' }, { status: 400 })
    }

    if (!salt) {
        console.error('No salt provided')
        // return json({ error: 'No salt provided' }, { status: 400 })
    }

    // Generate QR code as PNG buffer
    const qrBuffer = await QRCode.toBuffer(data, {
        errorCorrectionLevel: 'M',
        width: 200,
        margin: 1,
        type: 'png'
    })

    // Upload to Supabase Storage
    const fileName = `qr-${Date.now()}-${salt}.png`
    const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
        .from('qrcodes')
        .upload(fileName, qrBuffer, {
            contentType: 'image/png'
        })


    if (uploadError) {
        console.error('Upload error:', uploadError)
        // return json({ error: 'Failed to upload QR code' }, { status: 500 })
    }

    // console.log('Uploaded QR code:', uploadData)


    // Get public URL
    const { data: { publicUrl } } = supabaseAdmin.storage
        .from('qrcodes')
        .getPublicUrl(fileName)

    let url = publicUrl;



    // console.log('Returning from qr generateion: ', url)

    return { url };
}
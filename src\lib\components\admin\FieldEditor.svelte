<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Switch } from '$lib/components/ui/switch';

	import {
		Dialog,
		DialogContent,
		DialogDescription,
		DialogFooter,
		DialogHeader,
		DialogTitle
	} from '$lib/components/ui/dialog';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import { Plus, X, Trash2, AlertTriangle } from 'lucide-svelte';
	import type {
		FormField,
		FormFieldType,
		FieldOptions,
		ValidationRules
	} from '$lib/types/formFields';
	import { FIELD_TYPE_CONFIGS } from '$lib/types/formFields';

	// Props
	export let field: FormField;
	export let onSave: (updates: any) => void;
	export let onDelete: () => void;
	export let onCancel: () => void;

	// Form state - initialize with field values
	let fieldLabel = field.fieldLabel;
	let fieldType: FormFieldType = field.fieldType;
	let isRequired = field.isRequired;
	let isEnabled = field.isEnabled;
	let placeholderText = field.placeholderText || '';
	let helpText = field.helpText || '';
	let displayOrder = field.displayOrder;

	// Field options (for select/checkbox)
	let fieldOptions: string[] = field.fieldOptions?.options || [''];
	let allowMultiple = field.fieldOptions?.multiple || false;

	// Validation rules
	let minLength: number | undefined = field.validationRules?.minLength;
	let maxLength: number | undefined = field.validationRules?.maxLength;
	let minValue: number | undefined = field.validationRules?.min;
	let maxValue: number | undefined = field.validationRules?.max;
	let pattern = field.validationRules?.pattern || '';
	let customMessage = field.validationRules?.customMessage || '';

	// State
	let saving = false;
	let showDeleteConfirm = false;
	let errors: Record<string, string> = {};

	// Reactive values
	$: hasOptions = FIELD_TYPE_CONFIGS[fieldType]?.hasOptions || false;
	$: isValid = fieldLabel.trim() !== '';
	$: canDelete = !field.isStandardField;

	function addOption() {
		fieldOptions = [...fieldOptions, ''];
	}

	function removeOption(index: number) {
		fieldOptions = fieldOptions.filter((_, i) => i !== index);
	}

	function updateOption(index: number, value: string) {
		fieldOptions[index] = value;
		fieldOptions = [...fieldOptions];
	}

	async function handleSave() {
		if (!isValid) return;

		saving = true;
		errors = {};

		try {
			// Prepare field options
			let options: FieldOptions | undefined;
			if (hasOptions) {
				const validOptions = fieldOptions.filter((opt) => opt.trim() !== '');
				if (validOptions.length > 0) {
					options = {
						options: validOptions,
						multiple: fieldType === 'checkbox' ? allowMultiple : false
					};
				}
			}

			// Prepare validation rules
			let validationRules: ValidationRules | undefined;
			const rules: ValidationRules = {};

			if (minLength !== undefined && minLength > 0) rules.minLength = minLength;
			if (maxLength !== undefined && maxLength > 0) rules.maxLength = maxLength;
			if (minValue !== undefined) rules.min = minValue;
			if (maxValue !== undefined) rules.max = maxValue;
			if (pattern.trim() !== '') rules.pattern = pattern.trim();
			if (customMessage.trim() !== '') rules.customMessage = customMessage.trim();

			if (Object.keys(rules).length > 0) {
				validationRules = rules;
			}

			// Prepare updates
			const updates = {
				fieldLabel: fieldLabel.trim(),
				fieldType,
				fieldOptions: options,
				isRequired,
				isEnabled,
				displayOrder,
				validationRules,
				placeholderText: placeholderText.trim() || null,
				helpText: helpText.trim() || null
			};

			onSave(updates);
		} catch (error) {
			console.error('Error saving field:', error);
			errors.general = 'Failed to save field. Please try again.';
		} finally {
			saving = false;
		}
	}

	function handleDelete() {
		showDeleteConfirm = true;
	}

	function confirmDelete() {
		onDelete();
	}

	function handleCancel() {
		onCancel();
	}
</script>

<Dialog open={true} onOpenChange={handleCancel}>
	<DialogContent class="max-h-[90vh] max-w-2xl overflow-y-auto">
		<DialogHeader>
			<DialogTitle>Edit Field: {field.fieldLabel}</DialogTitle>
			<DialogDescription>
				Modify the settings for this form field.
				{#if field.isStandardField}
					<Badge variant="secondary" class="ml-2">Standard Field</Badge>
				{/if}
			</DialogDescription>
		</DialogHeader>

		<div class="space-y-6">
			<!-- Basic Information -->
			<Card>
				<CardHeader>
					<CardTitle class="text-lg">Basic Information</CardTitle>
				</CardHeader>
				<CardContent class="space-y-4">
					<!-- Field Label -->
					<div class="space-y-2">
						<Label for="fieldLabel">Field Label *</Label>
						<Input
							id="fieldLabel"
							bind:value={fieldLabel}
							placeholder="Enter field label"
							class={errors.fieldLabel ? 'border-red-500' : ''}
						/>
						{#if errors.fieldLabel}
							<p class="text-sm text-red-500">{errors.fieldLabel}</p>
						{/if}
						<p class="text-xs text-muted-foreground">
							Field key: <code class="rounded bg-muted px-1">{field.fieldKey}</code>
						</p>
					</div>

					<!-- Field Type (only for custom fields) -->
					{#if !field.isStandardField}
						<div class="space-y-2">
							<Label for="fieldType">Field Type *</Label>
							<select
								id="fieldType"
								bind:value={fieldType}
								class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
							>
								{#each Object.entries(FIELD_TYPE_CONFIGS) as [type, config]}
									<option value={type}>{config.label} - {config.description}</option>
								{/each}
							</select>
						</div>
					{:else}
						<div class="space-y-2">
							<Label>Field Type</Label>
							<div class="rounded bg-muted p-2">
								<Badge variant="outline">{FIELD_TYPE_CONFIGS[fieldType]?.label || fieldType}</Badge>
								<p class="mt-1 text-xs text-muted-foreground">
									Standard field type cannot be changed
								</p>
							</div>
						</div>
					{/if}

					<!-- Settings -->
					<div class="space-y-4">
						<div class="flex items-center justify-between">
							<div class="space-y-0.5">
								<Label>Required Field</Label>
								<p class="text-sm text-muted-foreground">
									Users must fill this field to submit the form
								</p>
							</div>
							<Switch bind:checked={isRequired} />
						</div>

						<div class="flex items-center justify-between">
							<div class="space-y-0.5">
								<Label>Enabled</Label>
								<p class="text-sm text-muted-foreground">
									Show this field in the registration form
								</p>
							</div>
							<Switch bind:checked={isEnabled} />
						</div>
					</div>
				</CardContent>
			</Card>

			<!-- Field Options (for select/checkbox) -->
			{#if hasOptions}
				<Card>
					<CardHeader>
						<CardTitle class="text-lg">Field Options</CardTitle>
						<CardDescription>Define the available options for this field.</CardDescription>
					</CardHeader>
					<CardContent class="space-y-4">
						{#if fieldType === 'checkbox'}
							<div class="flex items-center justify-between">
								<div class="space-y-0.5">
									<Label>Allow Multiple Selections</Label>
									<p class="text-sm text-muted-foreground">
										Allow users to select multiple options
									</p>
								</div>
								<Switch bind:checked={allowMultiple} />
							</div>
						{/if}

						<div class="space-y-2">
							<Label>Options</Label>
							{#each fieldOptions as option, index}
								<div class="flex gap-2">
									<Input
										bind:value={option}
										placeholder="Enter option {index + 1}"
										oninput={(e: Event) =>
											updateOption(index, (e.target as HTMLInputElement).value)}
									/>
									{#if fieldOptions.length > 1}
										<Button variant="outline" size="sm" onclick={() => removeOption(index)}>
											<X class="h-4 w-4" />
										</Button>
									{/if}
								</div>
							{/each}
							<Button variant="outline" onclick={addOption} class="w-full">
								<Plus class="mr-2 h-4 w-4" />
								Add Option
							</Button>
						</div>
					</CardContent>
				</Card>
			{/if}

			<!-- Additional Settings -->
			<Card>
				<CardHeader>
					<CardTitle class="text-lg">Additional Settings</CardTitle>
				</CardHeader>
				<CardContent class="space-y-4">
					<div class="space-y-2">
						<Label for="placeholderText">Placeholder Text</Label>
						<Input
							id="placeholderText"
							bind:value={placeholderText}
							placeholder="Enter placeholder text..."
						/>
					</div>

					<div class="space-y-2">
						<Label for="helpText">Help Text</Label>
						<Textarea
							id="helpText"
							bind:value={helpText}
							placeholder="Additional instructions or help text for users..."
							rows={2}
						/>
					</div>

					<div class="space-y-2">
						<Label for="displayOrder">Display Order</Label>
						<Input
							id="displayOrder"
							type="number"
							bind:value={displayOrder}
							placeholder="0"
							min="0"
						/>
						<p class="text-xs text-muted-foreground">Lower numbers appear first in the form</p>
					</div>
				</CardContent>
			</Card>

			{#if errors.general}
				<div class="rounded bg-red-50 p-3 text-sm text-red-500">
					{errors.general}
				</div>
			{/if}
		</div>

		<DialogFooter class="flex justify-between">
			<div>
				{#if canDelete}
					<Button variant="destructive" onclick={handleDelete} disabled={saving}>
						<Trash2 class="mr-2 h-4 w-4" />
						Delete Field
					</Button>
				{/if}
			</div>
			<div class="flex gap-2">
				<Button variant="outline" onclick={handleCancel} disabled={saving}>Cancel</Button>
				<Button onclick={handleSave} disabled={!isValid || saving}>
					{#if saving}
						<div class="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
					{/if}
					Save Changes
				</Button>
			</div>
		</DialogFooter>
	</DialogContent>
</Dialog>

<!-- Delete Confirmation Dialog -->
{#if showDeleteConfirm}
	<Dialog open={true} onOpenChange={() => (showDeleteConfirm = false)}>
		<DialogContent>
			<DialogHeader>
				<DialogTitle class="flex items-center gap-2">
					<AlertTriangle class="h-5 w-5 text-red-500" />
					Delete Field
				</DialogTitle>
				<DialogDescription>
					Are you sure you want to delete the field "{field.fieldLabel}"? This action cannot be
					undone and will remove all associated data.
				</DialogDescription>
			</DialogHeader>
			<DialogFooter>
				<Button variant="outline" onclick={() => (showDeleteConfirm = false)}>Cancel</Button>
				<Button variant="destructive" onclick={confirmDelete}>Delete Field</Button>
			</DialogFooter>
		</DialogContent>
	</Dialog>
{/if}

<style>
	code {
		font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
		font-size: 0.85em;
	}
</style>

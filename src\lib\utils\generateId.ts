/**
 * Utility function to generate random unique IDs with configurable options.
 * Can be used for temporary identifiers, reference codes, etc.
 * 
 * @param length - The length of the ID (default: 10)
 * @param prefix - Optional prefix to add to the ID
 * @param useLetters - Whether to include letters in the ID (default: true)
 * @param useNumbers - Whether to include numbers in the ID (default: true)
 * @param useSpecial - Whether to include special characters in the ID (default: false)
 * @returns A randomly generated unique ID string
 */
export function generateUniqueId({
    length = 10,
    prefix = '',
    useLetters = true,
    useNumbers = true,
    useSpecial = false
}: {
    length?: number;
    prefix?: string;
    useLetters?: boolean;
    useNumbers?: boolean;
    useSpecial?: boolean;
} = {}): string {
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const special = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    let characters = '';
    if (useLetters) characters += letters;
    if (useNumbers) characters += numbers;
    if (useSpecial) characters += special;
    
    // Default to alphanumeric if no character sets are selected
    if (characters === '') characters = letters + numbers;
    
    let result = prefix;
    const charactersLength = characters.length;
    
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    
    return result;
}

/**
 * Generates a short reference code that can be used for event references, 
 * confirmation codes, etc.
 * 
 * @param prefix - Optional prefix for the reference code
 * @returns A reference code in the format PREFIX-XXXX-XXXX
 */
export function generateReferenceCode(prefix: string = ''): string {
    const part1 = generateUniqueId({ length: 4, useLetters: true, useNumbers: true, useSpecial: false });
    const part2 = generateUniqueId({ length: 4, useLetters: true, useNumbers: true, useSpecial: false });
    
    return prefix ? `${prefix}-${part1}-${part2}` : `${part1}-${part2}`;
}

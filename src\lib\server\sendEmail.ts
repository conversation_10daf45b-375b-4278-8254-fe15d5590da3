import { SendMailClient } from 'zeptomail';
import { ZEPTO_TOKEN } from '$env/static/private';
import { formatDate, formatTime } from '$lib/utils/formatdatetime';

export async function sendEmailTemplate(firstName: string, lastName: string, email: string, qrLink: string, eventName: string, startDate: string, endDate: string, startTime: string, endTime: string, location: string, locationUrl: string) {
    console.log('Send Email Function Started')
    // console.log('Name: ', name)
    // console.log('Email: ', email)
    // console.log('QR Link: ', qrLink)
    // console.log('Event Name: ', eventName)
    // console.log('Start Date: ', startDate)
    // console.log('End Date: ', endDate)
    // console.log('Start Time: ', startTime)
    // console.log('End Time: ', endTime)
    // console.log('Location: ', location)
    // console.log('Location Url: ', locationUrl)

    const url = 'api.zeptomail.com/';
    const token = ZEPTO_TOKEN;

    const client = new SendMailClient({ url, token });

    let name = `${firstName} ${lastName}`;

    try {
        const resp = await client.sendMailWithTemplate({
            template_key: '2d6f.44892bd5626c3d76.k1.e853de70-aa79-11ef-8f37-525400336d52.1935ed16dd7',
            template_alias: 'qdbeventsehariscom',
            from: {
                address: '<EMAIL>',
                name: 'Event Guest Relations | eHaris ',
            },
            to: [
                {
                    email_address: {
                        address: email,
                        name: name,
                    },
                },
            ],
            merge_info: {
                name: name,
                qrcode: qrLink,
                eventName: eventName,
                startDate: formatDate(startDate),
                endDate: formatDate(endDate),
                startTime: formatTime(startTime),
                endTime: formatTime(endTime),
                location: location,
                locationUrl: locationUrl,
            },
            reply_to: [
                {
                    address: '<EMAIL>',
                    name: 'Event Guest Relations | eHaris',
                },
            ],
        });

        // console.log('Email sent successfully:', resp);
        return { success: true, response: resp };
    } catch (error) {
        console.error('Error sending email:', error);
        return { success: false, error };
    }




    // client
    //     .sendMailWithTemplate({
    //         template_key: '2d6f.44892bd5626c3d76.k1.e853de70-aa79-11ef-8f37-525400336d52.1935ed16dd7',
    //         template_alias: 'qdbeventsehariscom',
    //         from: {
    //             address: '<EMAIL>',
    //             name: 'Event Guest Relations | Qatar Development Bank'
    //         },
    //         to: [
    //             {
    //                 email_address: {
    //                     address: email,
    //                     name: name
    //                 }
    //             }
    //         ],

    //         merge_info: {
    //             name: name,
    //             // email: email,
    //             qrcode: qrLink,
    //             eventName: eventName,
    //             startDate: formatDate(startDate),
    //             endDate: formatDate(endDate),
    //             startTime: formatTime(startTime),
    //             endTime: formatTime(endTime),
    //             location: location,
    //             locationUrl: locationUrl
    //         },
    //         reply_to: [
    //             {
    //                 address: '<EMAIL>',
    //                 name: 'Event Guest Relations | Qatar Development Bank'
    //             }
    //         ]
    //     })
    //     // .then((resp) => console.log('Sending email success.', resp))
    //     .then((resp) => { return { resp }; })
    //     .catch((error) => console.log('Error sending email: ', error));
}


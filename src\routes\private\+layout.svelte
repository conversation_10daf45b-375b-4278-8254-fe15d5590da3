<script>
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { analytics } from '$lib/analytics';

	export let data;
	$: ({ supabase } = data);

	// Track admin page views
	$: if ($page.url.pathname.startsWith('/private/')) {
		const adminPage = $page.url.pathname.replace('/private/', '');
		const eventId = $page.params.eventid || undefined;
		analytics.trackAdminPageView(adminPage, eventId);
	}

	// Track successful login when admin layout loads
	onMount(() => {
		if (data.session?.user) {
			analytics.trackLoginSuccess(data.session.user.id);
			analytics.identifyUser(data.session.user.id, {
				email: data.session.user.email,
				role: 'admin'
			});
		}
	});

	$: logout = async () => {
		// Track logout attempt
		analytics.trackLogout();
		analytics.resetUser();

		const { error } = await supabase.auth.signOut();
		if (error) {
			console.error(error);
		}
		window.location.href = '/';
	};
</script>

<div class="flex h-screen w-screen">
	<!-- Sidebar Navigation -->
	<nav class="ml-3 w-36 bg-white shadow-md">
		<!-- <div class="p-4">
			<h1 class="text-2xl font-semibold text-gray-800">Admin Panel</h1>
		</div> -->
		<ul class=" mt-4">
			<li>
				<a
					href="/private/events"
					class="block px-4 py-2 text-gray-600 hover:bg-gray-200"
					onclick={() => analytics.trackButtonClick('admin_nav_events', 'admin_sidebar')}
				>
					Events
				</a>
			</li>
			<li>
				<a
					href="/private/users"
					class="block px-4 py-2 text-gray-600 hover:bg-gray-200"
					onclick={() => analytics.trackButtonClick('admin_nav_users', 'admin_sidebar')}
				>
					Users
				</a>
			</li>

			<li>
				<button onclick={logout} class="block px-4 py-2 text-gray-600 hover:bg-gray-200">
					Logout
				</button>
			</li>
		</ul>
	</nav>

	<!-- Main Content Area -->
	<main class="flex-1 p-8">
		<slot />
	</main>
</div>

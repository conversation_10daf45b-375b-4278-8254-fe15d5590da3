<script lang="ts">
	import Ellipsis from 'lucide-svelte/icons/ellipsis';
	import { Button } from '$lib/components/ui/button/index.js';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
	import { supabase } from '$lib/db/supabaseClient';
	import * as Dialog from '$lib/components/ui/dialog/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import * as Select from '$lib/components/ui/select/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import Upload from 'lucide-svelte/icons/upload';
	import toast, { Toaster } from 'svelte-hot-french-toast';

	let { id }: { id: string } = $props();

	// Modal state
	let nationalIdModalOpen = $state(false);
	let identityType = $state('');
	let nationalId = $state('');
	let passport = $state('');
	let nationality = $state('');
	let dateOfBirth = $state('');

	// Photo upload states
	let frontPhoto: File | null = $state(null);
	let backPhoto: File | null = $state(null);

	function handleFrontPhotoUpload(event: Event) {
		const input = event.target as HTMLInputElement;
		if (input.files && input.files.length > 0) {
			frontPhoto = input.files[0];
		}
	}

	function handleBackPhotoUpload(event: Event) {
		const input = event.target as HTMLInputElement;
		if (input.files && input.files.length > 0) {
			backPhoto = input.files[0];
		}
	}

	async function uploadPhoto(file: File, type: 'front' | 'back') {
		if (!file) return null;

		try {
			const fileExt = file.name.split('.').pop();
			const fileName = `${id}_${type}_${Date.now()}.${fileExt}`;
			const filePath = `${id}/identity_${type}_${fileName}`;

			const { error: uploadError } = await supabase.storage
				.from('guest-identity-docs')
				.upload(filePath, file);

			if (uploadError) throw uploadError;

			const {
				data: { publicUrl },
				error: urlError
			} = supabase.storage.from('guest-identity-docs').getPublicUrl(filePath);

			if (urlError) throw urlError;

			return publicUrl;
		} catch (error) {
			console.error(`Error uploading ${type} photo:`, error);
			toast.error(`Failed to upload ${type} photo`);
			return null;
		}
	}

	async function checkIn(id) {
		console.log('Checking in guest: ', id);
		const { data: checkinData, error: checkinError } = await supabase
			.from('guests')
			.update({ status: 'Checked-In', check_in_date_time: new Date() })
			.eq('id', id);
		if (checkinError) {
			console.error('Error checking in guest: ', checkinError);
		}
		console.log('Guest checked in: ', checkinData);
	}

	async function checkOut(id) {
		console.log('Checking out guest: ', id);
		const { data: checkoutData, error: checkoutError } = await supabase
			.from('guests')
			.update({ status: 'Checked-Out' })
			.eq('id', id);
		if (checkoutError) {
			console.error('Error checking out guest: ', checkoutError);
		}
		console.log('Guest checked out: ', checkoutData);
	}

	function viewGuestDetails(id) {
		console.log('Viewing guest details: ', id);
	}

	async function nationsalId(id) {
		console.log('National ID: ', id);
	}

	// async function handleNationalIdSubmit() {
	// 	const identityDetails = {
	// 		type: identityType,
	// 		nationalId: identityType === 'national-id' ? nationalId : '',
	// 		passport: identityType === 'passport' ? passport : '',
	// 		nationality: identityType === 'passport' ? nationality : '',
	// 		dateOfBirth
	// 	};

	// 	try {
	// 		// Update guest record with identity details
	// 		const { error } = await supabase
	// 			.from('guests')
	// 			.update({
	// 				identity_type: identityDetails.type,
	// 				national_id: identityDetails.nationalId,
	// 				passport_number: identityDetails.passport,
	// 				nationality: identityDetails.nationality,
	// 				date_of_birth: identityDetails.dateOfBirth
	// 			})
	// 			.eq('id', id);

	// 		if (error) throw error;

	// 		console.log('National ID Details saved for guest: ', id);
	// 		nationalIdModalOpen = false;
	// 	} catch (error) {
	// 		console.error('Error saving national ID details:', error);
	// 	}
	// }

	async function handleNationalIdSubmit() {
		// Validate required fields based on identity type
		if (!identityType) {
			toast.error('Please select an identity type');
			return;
		}

		if (identityType === 'national-id' && !nationalId) {
			toast.error('Please enter National ID');
			return;
		}

		if (identityType === 'passport' && (!passport || !nationality)) {
			toast.error('Please enter Passport Number and Nationality');
			return;
		}

		try {
			// Upload photos if present
			const frontPhotoUrl = frontPhoto ? await uploadPhoto(frontPhoto, 'front') : null;
			const backPhotoUrl = backPhoto ? await uploadPhoto(backPhoto, 'back') : null;

			// Update guest record with identity details
			const { error } = await supabase
				.from('guests')
				.update({
					identity_type: identityType,
					national_id: nationalId,
					passport_number: passport,
					nationality: nationality,
					date_of_birth: dateOfBirth,
					front_id_photo_url: frontPhotoUrl,
					back_id_photo_url: backPhotoUrl
				})
				.eq('id', id);

			if (error) throw error;

			toast.success('Identity details saved successfully');
			nationalIdModalOpen = false;
		} catch (error) {
			console.error('Error saving national ID details:', error);
			toast.error('Failed to save identity details');
		}
	}

	function openNationalIdModal() {
		nationalIdModalOpen = true;
	}
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger>
		{#snippet child({ props })}
			<Button {...props} variant="ghost" size="icon" class="relative size-8 p-0">
				<span class="sr-only">Open menu</span>
				<Ellipsis class="size-4" />
			</Button>
		{/snippet}
	</DropdownMenu.Trigger>
	<DropdownMenu.Content>
		<DropdownMenu.Group>
			<DropdownMenu.GroupHeading>Actions</DropdownMenu.GroupHeading>
			<DropdownMenu.Item onclick={() => navigator.clipboard.writeText(id)}>
				Copy ID
			</DropdownMenu.Item>
		</DropdownMenu.Group>
		<DropdownMenu.Separator />
		<DropdownMenu.Item onclick={() => checkIn(id)}>Check-In Guest</DropdownMenu.Item>
		<DropdownMenu.Item onclick={() => checkOut(id)}>Check-Out Guest</DropdownMenu.Item>
		<DropdownMenu.Item onclick={openNationalIdModal}>National Id</DropdownMenu.Item>
	</DropdownMenu.Content>
</DropdownMenu.Root>

<!-- National ID Modal -->
<Dialog.Root bind:open={nationalIdModalOpen}>
	<Dialog.Content>
		<Dialog.Header>
			<Dialog.Title>Identity Information</Dialog.Title>
		</Dialog.Header>

		<div class="grid gap-4 py-4">
			<!-- Identity Type Selection -->
			<div class="grid grid-cols-4 items-center gap-4">
				<Label class="text-right">Type</Label>
				<Select.Root type="single" bind:value={identityType}>
					<Select.Trigger class="col-span-3"
						>{identityType ? identityType : 'Select Identity Type'}</Select.Trigger
					>
					<Select.Content>
						<Select.Item value="national-id">National ID</Select.Item>
						<Select.Item value="passport">Passport</Select.Item>
					</Select.Content>
				</Select.Root>
			</div>

			<!-- National ID Input -->
			{#if identityType === 'national-id'}
				<div class="grid grid-cols-4 items-center gap-4">
					<Label class="text-right">National ID <span class="text-red-500">*</span></Label>
					<Input bind:value={nationalId} class="col-span-3" placeholder="Enter National ID" />
				</div>
			{/if}

			<!-- Passport Inputs -->
			{#if identityType === 'passport'}
				<div class="grid grid-cols-4 items-center gap-4">
					<Label class="text-right">Passport <span class="text-red-500">*</span></Label>
					<Input bind:value={passport} class="col-span-3" placeholder="Enter Passport Number" />
				</div>
				<div class="grid grid-cols-4 items-center gap-4">
					<Label class="text-right">Nationality <span class="text-red-500">*</span></Label>
					<Input bind:value={nationality} class="col-span-3" placeholder="Enter Nationality" />
				</div>
			{/if}

			<!-- Date of Birth (shown after identity type selection) -->
			{#if identityType}
				<div class="grid grid-cols-4 items-center gap-4">
					<Label class="text-right">Date of Birth</Label>
					<Input type="date" bind:value={dateOfBirth} class="col-span-3" />
				</div>
			{/if}

			<!-- Photo Upload (shown after identity type selection) -->
			{#if identityType}
				<div class="grid grid-cols-4 items-center gap-4">
					<Label class="text-right">Front Photo</Label>
					<div class="col-span-3 flex items-center gap-2">
						<Input
							type="file"
							accept="image/*"
							class="hidden"
							id="front-photo-upload"
							onchange={handleFrontPhotoUpload}
						/>
						<label for="front-photo-upload" class="flex items-center">
							<Button variant="outline" type="button">
								<Upload class="mr-2 size-4" />
								Upload Front
							</Button>
						</label>
						{#if frontPhoto}
							<span class="text-sm text-green-600">{frontPhoto.name}</span>
						{/if}
					</div>
				</div>
				<div class="grid grid-cols-4 items-center gap-4">
					<Label class="text-right">Back Photo</Label>
					<div class="col-span-3 flex items-center gap-2">
						<Input
							type="file"
							accept="image/*"
							class="hidden"
							id="back-photo-upload"
							onchange={handleBackPhotoUpload}
						/>
						<label for="back-photo-upload" class="flex items-center">
							<Button variant="outline" type="button">
								<Upload class="mr-2 size-4" />
								Upload Back
							</Button>
						</label>
						{#if backPhoto}
							<span class="text-sm text-green-600">{backPhoto.name}</span>
						{/if}
					</div>
				</div>
			{/if}
		</div>

		<Dialog.Footer>
			<Button variant="outline" onclick={() => (nationalIdModalOpen = false)}>Cancel</Button>
			<Button onclick={handleNationalIdSubmit}>Submit</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
<Toaster />

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request, locals: { supabase } }) => {
    try {
        const scanRecord = await request.json();

        // Get scanner info to check if validation is required
        const { data: scanner, error: scannerError } = await supabase
            .from('scanners')
            .select('requires_validation, gate')
            .eq('id', scanRecord.scanner_id)
            .single();

        if (scannerError) {
            console.error('Error fetching scanner info:', scannerError);
            return json({ success: false, error: 'Scanner not found' }, { status: 404 });
        }

        // Try to fetch badge information based on the QR data
        const { data: badgeData, error: badgeError } = await supabase
            .from('workforce')
            .select('first_name, last_name, category, badge_id,converted_url, zone_1, zone_2, zone_3, zone_4, zone_5, zone_6, fa_acc, fa_com, fa_crl, fa_eva, fa_fnp, fa_fbs, fa_gmh, fa_log, fa_mme, fa_ogc, fa_prg, fa_tml, fa_wkf')
            .eq('qr_data', scanRecord.qr_data)
            .single();

        // Validation logic
        if (scanner.requires_validation) {
            // Check if user exists
            if (badgeError || !badgeData) {
                return json({
                    success: false,
                    error: 'User not found',
                    validation_failed: true
                }, { status: 403 });
            }

            // Check zone access
            const scannerGate = scanner.gate;
            const hasAccess = checkZoneAccess(badgeData, scannerGate);

            if (!hasAccess) {
                return json({
                    success: false,
                    error: 'Access denied: No permission for this gate',
                    validation_failed: true,
                    user_data: badgeData
                }, { status: 403 });
            }
        }

        // Insert the scan record into the database
        const { data, error } = await supabase
            .from('scans')
            .insert({
                qr_data: scanRecord.qr_data,
                timestamp: scanRecord.timestamp,
                scanner_id: scanRecord.scanner_id,
                scanner_name: scanRecord.scanner_name,
                gate: scanRecord.gate,
                scan_type: scanRecord.scan_type
            })
            .select()
            .single();

        if (error) {
            console.error('Error recording scan:', error);
            return json({ success: false, error: error.message }, { status: 500 });
        }

        return json({
            success: true,
            data: badgeData || null,
            scan_id: data.id
        });
    } catch (error) {
        console.error('Error in record-scan endpoint:', error);
        return json({ success: false, error: 'Internal server error' }, { status: 500 });
    }
};

// Helper function to check if user has access to the gate
function checkZoneAccess(userData, gate) {
    // Map gate names to zone columns
    const gateToZoneMap = {
        'Zone 1': 'zone_1',
        'Zone 2': 'zone_2',
        'Zone 3': 'zone_3',
        'Zone 4': 'zone_4',
        'Zone 5': 'zone_5',
        'Zone 6': 'zone_6',
        // Add more mappings as needed
    };

    // If gate doesn't have a mapping, default to no access
    if (!gateToZoneMap[gate]) {
        return false;
    }

    // Check if user has access to this zone
    return userData[gateToZoneMap[gate]] === true;
}

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ url, locals: { supabase } }) => {
    try {
        const qrData = url.searchParams.get('qr_data');

        if (!qrData) {
            return json({ success: false, error: 'QR data is required' }, { status: 400 });
        }

        // Fetch the most recent scan for this QR code
        const { data: previousScans, error } = await supabase
            .from('scans')
            .select('id, qr_data, timestamp, scanner_name, gate, scan_type')
            .eq('qr_data', qrData)
            .order('timestamp', { ascending: false })
            .limit(5);  // Get the 5 most recent scans

        if (error) {
            console.error('Error fetching previous scans:', error);
            return json({ success: false, error: error.message }, { status: 500 });
        }

        return json({
            success: true,
            data: previousScans || []
        });
    } catch (error) {
        console.error('Error in previous-scans endpoint:', error);
        return json({ success: false, error: 'Internal server error' }, { status: 500 });
    }
};
# Event Management System

A modern event management system built with SvelteKit, Supabase, and TailwindCSS. This application helps organizations manage events, handle registrations, send automated emails, and process check-ins using QR codes.

## Features

- 🎫 Event Creation and Management
- 📧 Customizable Email Templates
- 📝 Guest Registration System
- 🔍 QR Code Check-in/Check-out
- 📊 Event Analytics Dashboard
- Design Certificates
- Auto Generate Certificate and Send to all Checked-in/out attendees.
- Manually Generate Certificate and download
- Bulk Import regesteration data via CSV and auto send emails
- Waiting List with Approval / Deny with email / Deny w.o email
- 📱 Responsive Design
- 📥 CSV Guest List Export
- 🔒 Authentication

## Tech Stack

- **Frontend**: SvelteKit 5.0
- **UI Components**: TailwindCSS, Bits UI
- **Database**: Supabase
- **Email**: ZeptoMail
- **PDF Generation**: PDFKit
- **QR Code**: QRCode.js
- **Form Validation**: Zod, Superforms
- **CSV Handling**: PapaParse

## Todo

- [ ] Authorization
- [ ] User Profiles
- [ ] User Roles
- [ ] Leads Generation
- [ ] Rework Navigation UI
- [ ] Page for past events
- [ ] Change the create event input to MD or HTML for better WYSWYG
- [ ] Allow admin to create custom regesteration feilds
- [ ] Allow admin to add paid tickets
- [ ] Allow admin to connect different payment gateway systems
- [ ] Cron Job for Auto-Archiving past events

## Getting Started

1. **Clone the repository**

   ```bash
   git clone [repository-url]
   cd org-events-template
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Environment Setup**
   Create a `.env` file in the root directory:

   ```env
   PUBLIC_SUPABASE_URL=your_supabase_url
   PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   PUBLIC_SITE_KEY=your_turnstile_site_key
   PUBLIC_SITE_KEY_LOCALHOST=your_turnstile_localhost_key
   PUBLIC_POSTHOG_API_KEY=your_posthog_api_key
   PUBLIC_POSTHOG_HOST=https://us.i.posthog.com
   ```

4. **Development**

   ```bash
   npm run dev
   ```

5. **Build**
   ```bash
   npm run build
   ```

## Project Structure

```
src/
├── lib/
│   ├── components/    # Reusable UI components
│   ├── server/       # Server-side utilities
│   └── utils/        # Helper functions
├── routes/
│   ├── private/      # Protected routes
│   └── [slug]/       # Public event pages
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

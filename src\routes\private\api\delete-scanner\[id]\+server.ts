import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';

export const DELETE: RequestHandler = async ({ params, locals: { supabase } }) => {
    try {
        const { id } = params;

        // Update scanner_id to null in scans table
        const { error: scansUpdateError } = await supabase
            .from('scans')
            .update({ scanner_id: null })
            .eq('scanner_id', id);

        if (scansUpdateError) {
            console.error('Error updating related scans:', scansUpdateError);
            return json({ success: false, error: scansUpdateError.message }, { status: 500 });
        }

        // Update scanner_id to null in invalid_scans table
        const { error: invalidScansUpdateError } = await supabase
            .from('invalid_scans')
            .update({ scanner_id: null })
            .eq('scanner_id', id);

        if (invalidScansUpdateError) {
            console.error('Error updating related invalid scans:', invalidScansUpdateError);
            // Continue anyway as this is not critical
        }

        // Now delete the scanner
        const { error } = await supabase
            .from('scanners')
            .delete()
            .eq('id', id);

        if (error) {
            console.error('Error deleting scanner:', error);
            return json({ success: false, error: error.message }, { status: 500 });
        }

        return json({ success: true });
    } catch (error) {
        console.error('Error in delete-scanner endpoint:', error);
        return json({ success: false, error: 'Internal server error' }, { status: 500 });
    }
};

<script lang="ts">
	import { onMount } from 'svelte';
	import { But<PERSON> } from '$lib/components/ui/button';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '$lib/components/ui/tabs';
	import { Badge } from '$lib/components/ui/badge';
	import toast, { Toaster } from 'svelte-hot-french-toast';
	import { Plus, Settings, Eye, EyeOff, GripVertical } from 'lucide-svelte';
	import StandardFieldToggle from './StandardFieldToggle.svelte';
	import CustomFieldBuilder from './CustomFieldBuilder.svelte';
	import FieldEditor from './FieldEditor.svelte';
	import type { FormField } from '$lib/types/formFields';
	import { STANDARD_FIELDS } from '$lib/types/formFields';

	// Props
	export let eventId: string;
	export let eventTitle: string = '';

	// State
	let formFields: FormField[] = [];
	let loading = true;
	let error: string | null = null;
	let showCustomFieldBuilder = false;
	let editingField: FormField | null = null;

	// Reactive computations
	$: standardFields = formFields.filter((field) => field.isStandardField);
	$: customFields = formFields.filter((field) => !field.isStandardField);
	$: enabledFieldsCount = formFields.filter((field) => field.isEnabled).length;
	$: requiredFieldsCount = formFields.filter((field) => field.isRequired && field.isEnabled).length;

	// Load form fields on mount
	onMount(async () => {
		await loadFormFields();
	});

	async function loadFormFields() {
		try {
			loading = true;
			error = null;

			const response = await fetch(`/private/api/events/${eventId}/form-fields`);
			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || 'Failed to load form fields');
			}

			formFields = result.data || [];

			// Check if all standard fields exist, if not, initialize them
			await ensureStandardFieldsExist();
		} catch (err) {
			console.error('Error loading form fields:', err);
			error = err instanceof Error ? err.message : 'Failed to load form fields';
			toast.error('Failed to load form fields');
		} finally {
			loading = false;
		}
	}

	async function ensureStandardFieldsExist() {
		try {
			// Check which standard fields are missing
			const existingFieldKeys = new Set(formFields.map((f) => f.fieldKey));
			const missingFields = STANDARD_FIELDS.filter((sf) => !existingFieldKeys.has(sf.fieldKey));

			if (missingFields.length > 0) {
				console.log(
					`Initializing ${missingFields.length} missing standard fields for event ${eventId}`
				);

				// Initialize standard fields (this will only create missing ones due to ON CONFLICT DO NOTHING)
				const initResponse = await fetch(`/private/api/events/${eventId}/form-fields/initialize`, {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' }
				});

				if (initResponse.ok) {
					// Reload form fields to get the newly created ones
					const reloadResponse = await fetch(`/private/api/events/${eventId}/form-fields`);
					const reloadResult = await reloadResponse.json();

					if (reloadResponse.ok) {
						formFields = reloadResult.data || [];
						console.log('Successfully initialized missing standard fields');
					}
				} else if (initResponse.status === 404) {
					// Fallback: endpoint doesn't exist, try to create missing fields individually
					console.log('Initialize endpoint not found, creating missing fields individually');
					await createMissingStandardFields(missingFields);
				}
			}
		} catch (err) {
			console.error('Error ensuring standard fields exist:', err);
			// Don't show error toast for this as it's a background operation
		}
	}

	async function createMissingStandardFields(missingFields: typeof STANDARD_FIELDS) {
		try {
			for (const standardField of missingFields) {
				const fieldData = {
					eventId: eventId,
					fieldKey: standardField.fieldKey,
					fieldLabel: standardField.fieldLabel,
					fieldType: standardField.fieldType,
					isRequired: standardField.isRequired,
					isStandardField: true,
					isEnabled: standardField.defaultEnabled,
					displayOrder: standardField.displayOrder,
					placeholderText: `Enter your ${standardField.fieldLabel.toLowerCase()}`
				};

				const response = await fetch(`/private/api/events/${eventId}/form-fields`, {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify(fieldData)
				});

				if (response.ok) {
					const result = await response.json();
					formFields = [...formFields, result.data];
					console.log(`Created missing standard field: ${standardField.fieldLabel}`);
				} else {
					console.error(`Failed to create standard field: ${standardField.fieldLabel}`);
				}
			}
		} catch (err) {
			console.error('Error creating missing standard fields:', err);
		}
	}

	async function handleStandardFieldToggle(fieldKey: string, enabled: boolean) {
		try {
			const field = formFields.find((f) => f.fieldKey === fieldKey);
			if (!field) {
				console.error(`Field not found: ${fieldKey}`);
				console.log(
					'Available fields:',
					formFields.map((f) => ({ key: f.fieldKey, label: f.fieldLabel }))
				);
				toast.error(
					`Field not found: ${fieldKey}. Try clicking "Initialize Missing Fields" first.`
				);
				return;
			}

			const response = await fetch(`/private/api/events/${eventId}/form-fields/${field.id}`, {
				method: 'PATCH',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ isEnabled: enabled })
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || 'Failed to update field');
			}

			// Update local state with the actual response data
			formFields = formFields.map((f) => (f.id === field.id ? result.data : f));

			// Show success message
			toast.success(`${field.fieldLabel} ${enabled ? 'enabled' : 'disabled'} successfully`);
		} catch (err) {
			console.error('Error updating field:', err);
			const errorMessage = err instanceof Error ? err.message : 'Failed to update field';
			toast.error(errorMessage);
		}
	}

	async function handleCustomFieldCreate(fieldData: any) {
		try {
			const response = await fetch(`/private/api/events/${eventId}/form-fields`, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(fieldData)
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || 'Failed to create field');
			}

			// Add new field to local state
			formFields = [...formFields, result.data];
			showCustomFieldBuilder = false;
			toast.success('Custom field created successfully');
		} catch (err) {
			console.error('Error creating field:', err);
			const errorMessage = err instanceof Error ? err.message : 'Failed to create field';
			toast.error(errorMessage);
		}
	}

	async function handleFieldUpdate(fieldId: string, updates: any) {
		try {
			const response = await fetch(`/private/api/events/${eventId}/form-fields/${fieldId}`, {
				method: 'PATCH',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(updates)
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || 'Failed to update field');
			}

			// Update local state
			formFields = formFields.map((f) => (f.id === fieldId ? result.data : f));
			editingField = null;
			toast.success('Field updated successfully');
		} catch (err) {
			console.error('Error updating field:', err);
			const errorMessage = err instanceof Error ? err.message : 'Failed to update field';
			toast.error(errorMessage);
		}
	}

	async function handleFieldDelete(fieldId: string) {
		if (!confirm('Are you sure you want to delete this field? This action cannot be undone.')) {
			return;
		}

		try {
			const response = await fetch(`/private/api/events/${eventId}/form-fields/${fieldId}`, {
				method: 'DELETE'
			});

			if (!response.ok) {
				const result = await response.json();
				throw new Error(result.error || 'Failed to delete field');
			}

			// Remove field from local state
			formFields = formFields.filter((f) => f.id !== fieldId);
			toast.success('Field deleted successfully');
		} catch (err) {
			console.error('Error deleting field:', err);
			const errorMessage = err instanceof Error ? err.message : 'Failed to delete field';
			toast.error(errorMessage);
		}
	}

	function handleFieldEdit(field: FormField) {
		editingField = field;
	}

	function handlePreviewForm() {
		// Open preview in new tab
		window.open(`/${eventId}?preview=true`, '_blank');
	}

	async function handleInitializeMissingFields() {
		try {
			await ensureStandardFieldsExist();
			toast.success('Missing standard fields initialized successfully');
		} catch (err) {
			console.error('Error initializing missing fields:', err);
			toast.error('Failed to initialize missing fields');
		}
	}
</script>

<div class="space-y-6">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold">Form Builder</h1>
			<p class="text-muted-foreground">
				Customize registration form for {eventTitle}
			</p>
		</div>
		<div class="flex gap-2">
			<Button variant="outline" onclick={handleInitializeMissingFields}>
				<Plus class="mr-2 h-4 w-4" />
				Initialize Missing Fields
			</Button>
			<Button variant="outline" onclick={handlePreviewForm}>
				<Eye class="mr-2 h-4 w-4" />
				Preview Form
			</Button>
		</div>
	</div>

	<!-- Stats Cards -->
	<div class="grid gap-4 md:grid-cols-3">
		<Card>
			<CardHeader class="pb-2">
				<CardTitle class="text-sm font-medium">Total Fields</CardTitle>
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold">{formFields.length}</div>
			</CardContent>
		</Card>
		<Card>
			<CardHeader class="pb-2">
				<CardTitle class="text-sm font-medium">Enabled Fields</CardTitle>
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold">{enabledFieldsCount}</div>
			</CardContent>
		</Card>
		<Card>
			<CardHeader class="pb-2">
				<CardTitle class="text-sm font-medium">Required Fields</CardTitle>
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold">{requiredFieldsCount}</div>
			</CardContent>
		</Card>
	</div>

	{#if loading}
		<div class="flex items-center justify-center py-8">
			<div class="text-center">
				<div class="mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
				<p class="text-muted-foreground">Loading form fields...</p>
			</div>
		</div>
	{:else if error}
		<Card>
			<CardContent class="pt-6">
				<div class="text-center text-red-600">
					<p>Error: {error}</p>
					<Button variant="outline" onclick={loadFormFields} class="mt-2">Try Again</Button>
				</div>
			</CardContent>
		</Card>
	{:else}
		<!-- Main Content -->
		<Tabs value="standard" class="space-y-4">
			<TabsList>
				<TabsTrigger value="standard">Standard Fields</TabsTrigger>
				<TabsTrigger value="custom">Custom Fields</TabsTrigger>
			</TabsList>

			<TabsContent value="standard" class="space-y-4">
				<Card>
					<CardHeader>
						<CardTitle>Standard Fields</CardTitle>
						<CardDescription>
							Enable or disable predefined standard fields for your event registration form.
						</CardDescription>
					</CardHeader>
					<CardContent class="space-y-4">
						{#each STANDARD_FIELDS as standardFieldConfig}
							{@const field = standardFields.find(
								(f) => f.fieldKey === standardFieldConfig.fieldKey
							)}
							<StandardFieldToggle
								{standardFieldConfig}
								{field}
								onToggle={(enabled) =>
									handleStandardFieldToggle(standardFieldConfig.fieldKey, enabled)}
								onEdit={() => field && handleFieldEdit(field)}
							/>
						{/each}
					</CardContent>
				</Card>
			</TabsContent>

			<TabsContent value="custom" class="space-y-4">
				<div class="flex items-center justify-between">
					<div>
						<h3 class="text-lg font-medium">Custom Fields</h3>
						<p class="text-sm text-muted-foreground">
							Create custom fields specific to your event needs.
						</p>
					</div>
					<Button onclick={() => (showCustomFieldBuilder = true)}>
						<Plus class="mr-2 h-4 w-4" />
						Add Custom Field
					</Button>
				</div>

				{#if customFields.length === 0}
					<Card>
						<CardContent class="pt-6">
							<div class="text-center text-muted-foreground">
								<p>No custom fields created yet.</p>
								<p class="text-sm">Click "Add Custom Field" to create your first custom field.</p>
							</div>
						</CardContent>
					</Card>
				{:else}
					<div class="space-y-3">
						{#each customFields as field}
							<Card>
								<CardContent class="pt-4">
									<div class="flex items-center justify-between">
										<div class="flex items-center gap-3">
											<GripVertical class="h-4 w-4 cursor-move text-muted-foreground" />
											<div>
												<div class="flex items-center gap-2">
													<span class="font-medium">{field.fieldLabel}</span>
													{#if field.isRequired}
														<Badge variant="destructive" class="text-xs">Required</Badge>
													{/if}
													{#if !field.isEnabled}
														<Badge variant="secondary" class="text-xs">Disabled</Badge>
													{/if}
												</div>
												<p class="text-sm text-muted-foreground">
													{field.fieldType} • {field.fieldKey}
												</p>
											</div>
										</div>
										<div class="flex items-center gap-2">
											<Button
												variant="ghost"
												size="sm"
												onclick={() => handleStandardFieldToggle(field.fieldKey, !field.isEnabled)}
											>
												{#if field.isEnabled}
													<EyeOff class="h-4 w-4" />
												{:else}
													<Eye class="h-4 w-4" />
												{/if}
											</Button>
											<Button variant="ghost" size="sm" onclick={() => handleFieldEdit(field)}>
												<Settings class="h-4 w-4" />
											</Button>
										</div>
									</div>
								</CardContent>
							</Card>
						{/each}
					</div>
				{/if}
			</TabsContent>
		</Tabs>
	{/if}
</div>

<!-- Custom Field Builder Modal -->
{#if showCustomFieldBuilder}
	<CustomFieldBuilder
		{eventId}
		onSave={handleCustomFieldCreate}
		onCancel={() => (showCustomFieldBuilder = false)}
	/>
{/if}

<!-- Field Editor Modal -->
{#if editingField}
	<FieldEditor
		field={editingField}
		onSave={(updates) => handleFieldUpdate(editingField!.id, updates)}
		onDelete={() => handleFieldDelete(editingField!.id)}
		onCancel={() => (editingField = null)}
	/>
{/if}

<!-- Toast notifications -->
<Toaster />
